"use client";

import React, { useEffect, useState, useCallback } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { format } from "date-fns";
import { axiosInstance } from "@/lib/axios";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import Image from "next/image";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronDown, ChevronUp, Filter, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Di<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";

interface Transaction {
  id: string;
  type: "CREDIT" | "DEBIT";
  amount: number;
  reason: string;
  createdAt: string;
}

const formSchema = z.object({
  amount: z
    .string()
    .min(1, "Amount is required")
    .refine(
      (val) => {
        const num = parseInt(val, 10);
        return !isNaN(num) && num >= 1 && num <= 10000;
      },
      { message: "Amount must be between ₹1 and ₹10,000" }
    ),
});

const transferSchema = z.object({
  amount: z
    .string()
    .min(1, "Amount is required")
    .refine(
      (val) => {
        const num = parseInt(val, 10);
        return !isNaN(num) && num >= 1 && num <= 10000;
      },
      { message: "Amount must be between 1 and 10,000 coins" }
    ),
  reason: z.string().optional(),
});

const checkSchema = z.object({
  recipientContact: z
    .string()
    .min(10, "Contact number must be 10 digits")
    .max(10, "Contact number must be 10 digits")
    .regex(/^\d+$/, "Contact number must contain only digits"),
});

const CoinsPage = () => {
  const [coins, setCoins] = useState<number>(0);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<"all" | "credit" | "debit">("all");
  const [sortByDate, setSortByDate] = useState<"asc" | "desc">("desc");
  const { user }: any = useSelector((state: RootState) => state.user);
  const [isPaying, setIsPaying] = useState(false);
  const [isTransferOpen, setIsTransferOpen] = useState(false);
  const [recipientFound, setRecipientFound] = useState(false);
  const [recipientContact, setRecipientContact] = useState("");
  const [recipientName, setRecipientName] = useState<string>("");
  const [isCheckingRecipient, setIsCheckingRecipient] = useState(false);
  const [isTransferring, setIsTransferring] = useState(false);

  // Add Coins form
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm({
    resolver: zodResolver(formSchema),
  });

  // Check Student form
  const {
    register: registerCheck,
    handleSubmit: handleCheckSubmit,
    formState: { errors: checkErrors },
    reset: resetCheck,
  } = useForm({
    resolver: zodResolver(checkSchema),
    defaultValues: { recipientContact: "" },
  });

  // Transfer Coins form
  const {
    register: registerTransfer,
    handleSubmit: handleTransferSubmit,
    formState: { errors: transferErrors },
    reset: resetTransfer,
  } = useForm({
    resolver: zodResolver(transferSchema),
    defaultValues: { amount: "", reason: "" },
  });

  // Add coins
  const onSubmit = async (values: { amount: string }) => {
    await initiatePayment(parseInt(values.amount));
  };

  // Check recipient
  const checkRecipient = async (data: { recipientContact: string }) => {
    setIsCheckingRecipient(true);
    try {
      const response = await axiosInstance.post("/coins/check-student", {
        contact: data.recipientContact,
      });
      if (response.data.success && response.data.exists) {
        setRecipientFound(true);
        setRecipientContact(data.recipientContact);
        setRecipientName(response.data.studentName || "");
        toast.success(`Student found: ${response.data.studentName}`);
      } else {
        setRecipientFound(false);
        setRecipientName("");
        toast.error(response.data.message || "Student not found.");
      }
    } catch (error: any) {
      console.error("Error checking recipient:", error);
      toast.error(
        `Failed to check student: ${
          error.response?.data?.message || error.message
        }`
      );
    } finally {
      setIsCheckingRecipient(false);
    }
  };

  // Transfer coins
  const onTransferSubmit = async (data: {
    amount: string;
    reason?: string;
  }) => {
    setIsTransferring(true);
    try {
      const response = await axiosInstance.post("/coins/transfer-coins", {
        recipientContact,
        amount: parseInt(data.amount),
        reason: data.reason?.trim()
          ? `${data.reason} (Coin transfer to: ${recipientName})`
          : `Coin transfer to ${recipientName}`,
      });
      if (response.data.success) {
        toast.success(`Coins transferred successfully to ${recipientName}!`);
        setIsTransferOpen(false);
        resetTransfer();
        resetCheck();
        setRecipientFound(false);
        setRecipientName("");
        fetchData();
      }
    } catch (error: any) {
      console.error("Error transferring coins:", error);
      toast.error(
        `Failed to transfer coins: ${
          error.response?.data?.message || error.message
        }`
      );
    } finally {
      setIsTransferring(false);
    }
  };

  // Payment initiation
  const initiatePayment = async (amt: number) => {
    const isStudentLoggedIn = localStorage.getItem("studentToken") !== null;
    const isStudent = user?.role === "STUDENT" || isStudentLoggedIn;

    setIsPaying(true);
    try {
      const createOrderEndpoint = isStudent
        ? "/coins/create-order"
        : "/coins/create-order/class";
      const res = await axiosInstance.post(createOrderEndpoint, {
        amount: amt * 100,
      });
      const { order } = res.data;

      const options = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
        amount: order.amount,
        currency: "INR",
        name: "Uest Coins",
        description: "Add Uest Coins",
        order_id: order.id,
        prefill: {
          name: user?.firstName + " " + user?.lastName,
          email: user?.email,
        },
        handler: async function (response: any) {
          try {
            const verifyEndpoint = isStudent
              ? "/coins/verify"
              : "/coins/verify/class";
            await axiosInstance.post(verifyEndpoint, {
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              amount: amt * 100,
            });
            toast.success("Coins added successfully!");
            fetchData();
            reset();
          } catch {
            toast.error("Payment verification failed");
          }
        },
        theme: { color: "#f97316" },
      };

      const rzp = new (window as any).Razorpay(options);
      rzp.open();
    } catch {
      toast.error("Payment initialization failed");
    } finally {
      setIsPaying(false);
    }
  };

  useEffect(() => {
    const script = document.createElement("script");
    script.src = "https://checkout.razorpay.com/v1/checkout.js";
    script.async = true;
    document.body.appendChild(script);
  }, []);

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const isStudentLoggedIn = localStorage.getItem("studentToken") !== null;
      const isStudent = user?.role === "STUDENT" || isStudentLoggedIn;
      const coinEndpoint = isStudent
        ? "/coins/get-total-coins/student"
        : "/coins/get-total-coins";
      const transEndpoint = isStudent
        ? "/coins/transaction-history/student"
        : "/coins/transaction-history";

      const [coinsResponse, transactionsResponse] = await Promise.all([
        axiosInstance.get(coinEndpoint),
        axiosInstance.get(transEndpoint),
      ]);

      setCoins(coinsResponse.data.coins);
      setTransactions(transactionsResponse.data.transactions);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to load coin data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [user?.role]);

  useEffect(() => {
    fetchData();
  }, [user?.id, fetchData]);

  useEffect(() => {
    const handleStorageChange = () => fetchData();
    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [fetchData]);

  const filteredTransactions = transactions
    .filter((txn) => filter === "all" || txn.type.toLowerCase() === filter)
    .sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return sortByDate === "desc" ? dateB - dateA : dateA - dateB;
    });

  const TransactionCard = ({ txn }: { txn: Transaction }) => {
    const [isExpanded, setIsExpanded] = useState(false);

    return (
      <Card
        className="p-4 bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h3 className="text-base font-semibold text-foreground">
              {txn.type === "CREDIT" ? (
                <span className="text-green-500">Credit</span>
              ) : (
                <span className="text-red-500">Debit</span>
              )}
            </h3>
            <p className="text-sm text-muted-foreground">
              {txn.amount} Coins •{" "}
              {format(new Date(txn.createdAt), "MMM dd, yyyy")}
            </p>
          </div>
          {isExpanded ? (
            <ChevronUp className="h-5 w-5 text-muted-foreground" />
          ) : (
            <ChevronDown className="h-5 w-5 text-muted-foreground" />
          )}
        </div>
        {isExpanded && (
          <div className="mt-3 pt-3 border-t text-sm text-muted-foreground space-y-1 animate-in fade-in">
            <p>
              <strong>Reason:</strong> {txn.reason}
            </p>
            <p>
              <strong>Time:</strong> {format(new Date(txn.createdAt), "p")}
            </p>
          </div>
        )}
      </Card>
    );
  };

  return (
    <>
      <Header />
      <div className="px-4 sm:px-6 lg:px-8 py-12 max-w-7xl mx-auto space-y-6">
        {/* Balance */}
        <div className="sticky top-16 z-10 bg-background/95 backdrop-blur-sm supports-[backdrop-filter]:bg-background/60 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="relative w-12 h-12 rounded-full bg-orange-100 p-2">
                <Image
                  src="/uest_coin.png"
                  alt="Coin Icon"
                  fill
                  className="object-contain"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground">
                  {typeof window !== "undefined" &&
                  localStorage.getItem("studentToken")
                    ? "Student"
                    : "Class"}{" "}
                  Uest Coin Balance
                </h1>
                <Badge
                  variant="outline"
                  className="text-customOrange text-xl font-semibold border-customOrange mt-1"
                >
                  {coins} Coins
                </Badge>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={fetchData}
              disabled={isLoading}
              className="flex gap-2 w-full sm:w-auto"
            >
              <RefreshCw
                className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
          </div>
        </div>

        {/* Add Coins */}
        <form onSubmit={handleSubmit(onSubmit)}>
          <Card className="p-6 bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200">
            <h2 className="text-lg font-semibold text-foreground mb-4">
              Add Uest Coins
            </h2>
            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
              <div className="space-y-4">
                <div className="relative w-full">
                  <Input
                    type="number"
                    placeholder="Enter amount (₹1 - ₹10,000)"
                    className="w-full border-customOrange/30 focus:border-customOrange pr-10"
                    disabled={isPaying || isSubmitting}
                    {...register("amount")}
                  />
                  <span className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground text-sm">
                    ₹
                  </span>
                  {errors.amount && (
                    <p className="text-sm text-red-500 mt-1">
                      {errors.amount.message}
                    </p>
                  )}
                </div>
                <div className="flex flex-col sm:flex-row justify-between gap-4">
                  <Button
                    type="submit"
                    disabled={isPaying || isSubmitting}
                    className={cn(
                      "w-full bg-customOrange hover:bg-orange-600 text-white",
                      (isPaying || isSubmitting) && "opacity-75 cursor-not-allowed"
                    )}
                  >
                    {(isPaying || isSubmitting) && (
                      <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                    )}
                    {isPaying || isSubmitting ? "Processing..." : "Add Coins"}
                  </Button>
                  <Button
                    type="button"
                    onClick={() => {
                      setIsTransferOpen(true);
                      resetTransfer();
                      resetCheck();
                      setRecipientFound(false);
                    }}
                    disabled={isPaying || isSubmitting}
                    className={cn(
                      "w-full bg-customOrange hover:bg-orange-600 text-white",
                      (isPaying || isSubmitting) && "opacity-75 cursor-not-allowed"
                    )}
                  >
                    Transfer Coins
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </form>

        {/* Transfer Dialog */}
        <Dialog
          open={isTransferOpen}
          onOpenChange={(open) => {
            setIsTransferOpen(open);
            if (!open) {
              resetTransfer();
              resetCheck();
              setRecipientFound(false);
            }
          }}
        >
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Transfer Coins</DialogTitle>
              <DialogDescription>
                {recipientFound
                  ? "Enter amount and reason to transfer coins."
                  : "Enter the recipient's mobile number to transfer coins."}
              </DialogDescription>
            </DialogHeader>

            {!recipientFound ? (
              <form onSubmit={handleCheckSubmit(checkRecipient)}>
                <div className="space-y-4">
                  <div>
                    <Input
                      type="text"
                      placeholder="Enter mobile number"
                      {...registerCheck("recipientContact")}
                      className="w-full"
                    />
                    {checkErrors.recipientContact && (
                      <p className="text-sm text-red-500 mt-1">
                        {checkErrors.recipientContact.message}
                      </p>
                    )}
                  </div>
                  <Button
                    type="submit"
                    disabled={isCheckingRecipient}
                    className="w-full"
                  >
                    {isCheckingRecipient ? "Checking..." : "Check Student"}
                  </Button>
                </div>
              </form>
            ) : (
              <div>
                <div className="mb-4 p-3 rounded-md">
                  <p className="text-sm">
                    <strong>Transferring to:</strong> {recipientName}
                  </p>
                  <p className="text-sm mt-1">
                    Mobile: {recipientContact}
                  </p>
                </div>
                <form onSubmit={handleTransferSubmit(onTransferSubmit)}>
                  <div className="space-y-4">
                    <div>
                      <Input
                        type="number"
                        placeholder="Enter amount (1 - 10,000)"
                        {...registerTransfer("amount")}
                        className="w-full"
                      />
                      {transferErrors.amount && (
                        <p className="text-sm text-red-500 mt-1">
                          {transferErrors.amount.message}
                        </p>
                      )}
                    </div>
                    <div>
                      <Input
                        type="text"
                        placeholder="Reason (optional)"
                        {...registerTransfer("reason")}
                        className="w-full"
                      />
                    </div>
                    <Button
                      type="submit"
                      disabled={isTransferring}
                      className="w-full"
                    >
                      {isTransferring ? "Transferring..." : `Transfer to ${recipientName}`}
                    </Button>
                  </div>
                </form>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Transactions */}
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-20 w-full rounded-lg" />
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full rounded-lg" />
            ))}
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              <div className="flex gap-2 flex-wrap">
                <Button
                  variant={filter === "all" ? "default" : "outline"}
                  onClick={() => setFilter("all")}
                >
                  All
                </Button>
                <Button
                  variant={filter === "credit" ? "default" : "outline"}
                  onClick={() => setFilter("credit")}
                >
                  Credits
                </Button>
                <Button
                  variant={filter === "debit" ? "default" : "outline"}
                  onClick={() => setFilter("debit")}
                >
                  Debits
                </Button>
              </div>
              <Button
                variant="outline"
                onClick={() =>
                  setSortByDate(sortByDate === "desc" ? "asc" : "desc")
                }
              >
                <Filter className="mr-2 h-4 w-4" />
                {sortByDate === "desc" ? "Newest First" : "Oldest First"}
              </Button>
            </div>
            {filteredTransactions.length > 0 ? (
              <div className="grid gap-4">
                {filteredTransactions.map((txn) => (
                  <TransactionCard key={txn.id} txn={txn} />
                ))}
              </div>
            ) : (
              <p className="text-center text-muted-foreground">
                No transactions found
              </p>
            )}
          </div>
        )}
      </div>
      <Footer />
    </>
  );
};

export default CoinsPage;
