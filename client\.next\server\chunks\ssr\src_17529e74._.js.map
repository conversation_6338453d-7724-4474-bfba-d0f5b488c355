{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};\r\n\r\nexport const setStudentAuthToken = (token: string) => {\r\n  localStorage.setItem('studentToken', token);\r\n};\r\n\r\nexport const getStudentAuthToken = (): string | null => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('studentToken');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const clearStudentAuthToken = () => {\r\n  localStorage.removeItem('studentToken');\r\n};\r\n\r\nexport const isStudentAuthenticated = (): boolean => {\r\n  return !!getStudentAuthToken();\r\n};\r\n\r\nexport const isAuthenticated = (): { isAuth: boolean; userType: 'STUDENT' | 'CLASS' | null } => {\r\n  const studentToken = getStudentAuthToken();\r\n  if (studentToken) {\r\n    return { isAuth: true, userType: 'STUDENT' };\r\n  }\r\n\r\n  if (typeof window !== 'undefined') {\r\n    const userData = localStorage.getItem('user');\r\n    if (userData) {\r\n      try {\r\n        const user = JSON.parse(userData);\r\n        if (user && user.id) {\r\n          return { isAuth: true, userType: 'CLASS' };\r\n        }\r\n      } catch (error) {\r\n        console.error('Error parsing user data:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  return { isAuth: false, userType: null };\r\n};"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,sBAAsB;IACjC,uCAAmC;;IAEnC;IACA,OAAO;AACT;AAEO,MAAM,wBAAwB;IACnC,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAC,CAAC;AACX;AAEO,MAAM,kBAAkB;IAC7B,MAAM,eAAe;IACrB,IAAI,cAAc;QAChB,OAAO;YAAE,QAAQ;YAAM,UAAU;QAAU;IAC7C;IAEA,uCAAmC;;IAYnC;IAEA,OAAO;QAAE,QAAQ;QAAO,UAAU;IAAK;AACzC", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/hooks.ts"], "sourcesContent": ["import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\r\nimport type { RootState, AppDispatch } from './index';\r\n\r\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\r\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\r\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAIO,MAAM,iBAAiB,IAAM,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;AACvC,MAAM,iBAAkD,yJAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/ProfileCompletionIndicator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { User, ArrowRight } from 'lucide-react';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\n\r\nconst ProfileCompletionIndicator = () => {\r\n  const router = useRouter();\r\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\r\n\r\n  // Check if student is logged in\r\n  const isLoggedIn = typeof window !== 'undefined' && localStorage.getItem('studentToken') !== null;\r\n\r\n  // Check if student has any profile data - only check if profile ID exists\r\n  const hasProfile = profileData?.profile?.id !== undefined;\r\n\r\n  // Only show the indicator if student is logged in and doesn't have a profile\r\n  if (!isLoggedIn || hasProfile) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"my-4 mx-10 sm:px-4\">\r\n      <div className=\"bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden dark:border-orange-900\">\r\n        <div className=\"px-3 py-1.5 flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"bg-[#ff914d] p-1.5 rounded-full\">\r\n            <User className=\"h-3 w-3 text-white\" />\r\n          </div>\r\n          <p className=\"text-xs font-medium text-gray-800 dark:text-gray-200\">\r\n            Please Complete your profile \r\n          </p>\r\n        </div>\r\n        <Button\r\n          onClick={() => router.push('/student/profile')}\r\n          className=\"bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0\"\r\n          size=\"sm\"\r\n        >\r\n         Complete now <ArrowRight className=\"h-3 w-3\" />\r\n        </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileCompletionIndicator;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;;AASA,MAAM,6BAA6B;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,cAAc;IAE9E,gCAAgC;IAChC,MAAM,aAAa,gBAAkB,eAAe,aAAa,OAAO,CAAC,oBAAoB;IAE7F,0EAA0E;IAC1E,MAAM,aAAa,aAAa,SAAS,OAAO;IAEhD,6EAA6E;IAC7E,wCAA+B;QAC7B,OAAO;IACT;;AAyBF;uCAEe", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,2KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,2KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/notificationService.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport interface Notification {\r\n  id: string;\r\n  userId: string;\r\n  userType: 'STUDENT' | 'CLASS' | 'ADMIN';\r\n  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |\r\n        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |\r\n        'STUDENT_STORE_PURCHASE' | 'STUDENT_STORE_ORDER_APPROVED' | 'STUDENT_STORE_ORDER_REJECTED' |\r\n        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |\r\n        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |\r\n        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |\r\n        'CLASS_STORE_PURCHASE' | 'CLASS_STORE_ORDER_APPROVED' | 'CLASS_STORE_ORDER_REJECTED' |\r\n        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |\r\n        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED' | 'ADMIN_NEW_STORE_ORDER';\r\n  title: string;\r\n  message: string;\r\n  data?: any;\r\n  isRead: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface NotificationPagination {\r\n  currentPage: number;\r\n  totalPages: number;\r\n  totalCount: number;\r\n  limit: number;\r\n  hasNextPage: boolean;\r\n  hasPrevPage: boolean;\r\n}\r\n\r\nexport interface NotificationResponse {\r\n  notifications: Notification[];\r\n  pagination: NotificationPagination;\r\n}\r\n\r\n// For Classes (authenticated users)\r\nexport const getClassNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/classes?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getClassUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/classes/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markClassNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/classes/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllClassNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/classes/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllClassNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/classes/delete-all');\r\n  return response.data;\r\n};\r\n\r\n// For Students (bearer token auth)\r\nexport const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/students?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getStudentUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/students/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markStudentNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/students/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllStudentNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/students/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllStudentNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/students/delete-all');\r\n  return response.data;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAsCO,MAAM,wBAAwB,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAC9E,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC7F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,sBAAsB;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,gBAAgB;IAC9F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kCAAkC;IAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,8BAA8B;IACzC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,0BAA0B,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAChF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC9F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,wBAAwB;IACnC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,gCAAgC,OAAO;IAClD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,gBAAgB;IAC/F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oCAAoC;IAC/C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gCAAgC;IAC3C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/NotificationBell.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { Bell } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\n\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport {\r\n  getClassNotifications,\r\n  getClassUnreadCount,\r\n  markClassNotificationAsRead,\r\n  markAllClassNotificationsAsRead,\r\n  deleteAllClassNotifications,\r\n  getStudentNotifications,\r\n  getStudentUnreadCount,\r\n  markStudentNotificationAsRead,\r\n  markAllStudentNotificationsAsRead,\r\n  deleteAllStudentNotifications,\r\n  Notification\r\n} from '@/services/notificationService';\r\nimport { toast } from 'sonner';\r\nimport { formatDistanceToNow } from 'date-fns';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface NotificationBellProps {\r\n  userType: 'class' | 'student';\r\n}\r\n\r\nexport default function NotificationBell({ userType }: NotificationBellProps) {\r\n  const [notifications, setNotifications] = useState<Notification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n\r\n  const router = useRouter();\r\n\r\n  const safeNotifications = Array.isArray(notifications) ? notifications : [];\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      let result: any;\r\n      let count: number;\r\n\r\n      if (userType === 'class') {\r\n        result = await getClassNotifications(1, 20);\r\n        count = await getClassUnreadCount();\r\n      } else {\r\n        result = await getStudentNotifications(1, 20);\r\n        count = await getStudentUnreadCount();\r\n      }\r\n\r\n      // Handle both old and new response formats\r\n      const notifs = result?.notifications || result || [];\r\n      setNotifications(Array.isArray(notifs) ? notifs : []);\r\n      setUnreadCount(count);\r\n    } catch (error) {\r\n      console.error('Error fetching notifications:', error);\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [userType]);\r\n\r\n  const handleNotificationClick = async (notification: Notification) => {\r\n    try {\r\n      // Mark notification as read\r\n      if (userType === 'class') {\r\n        await markClassNotificationAsRead(notification.id);\r\n      } else {\r\n        await markStudentNotificationAsRead(notification.id);\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif =>\r\n          notif.id === notification.id ? { ...notif, isRead: true } : notif\r\n        )\r\n      );\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n      setIsOpen(false);\r\n      if (notification.data?.actionType === 'OPEN_CHAT' && notification.data?.redirectUrl) {\r\n        router.push(notification.data.redirectUrl);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error handling notification click:', error);\r\n      toast.error('Failed to process notification');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    try {\r\n      if (userType === 'class') {\r\n        await markAllClassNotificationsAsRead();\r\n      } else {\r\n        await markAllStudentNotificationsAsRead();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif => ({ ...notif, isRead: true }))\r\n      );\r\n      setUnreadCount(0);\r\n      toast.success('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      toast.error('Failed to mark all notifications as read');\r\n    }\r\n  };\r\n\r\n  const handleRemoveAllClick = () => {\r\n    setShowDeleteDialog(true);\r\n  };\r\n\r\n  const handleConfirmRemoveAll = async () => {\r\n    setShowDeleteDialog(false);\r\n\r\n    try {\r\n      if (userType === 'class') {\r\n        await deleteAllClassNotifications();\r\n      } else {\r\n        await deleteAllStudentNotifications();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n      toast.success('All notifications removed successfully');\r\n    } catch (error) {\r\n      console.error('Error removing all notifications:', error);\r\n      toast.error('Failed to remove all notifications');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchNotifications();\r\n\r\n    const interval = setInterval(fetchNotifications, 30000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [fetchNotifications]);\r\n\r\n  return (\r\n    <>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"relative bg-black hover:bg-gray-900 transition duration-200 h-9 w-9 md:h-10 md:w-10 rounded-full\"\r\n          >\r\n            <div className=\"absolute inset-0 rounded-full bg-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none\" />\r\n\r\n            <Bell className=\"relative z-10 h-4 w-4 md:h-5 md:w-5 text-white transition-colors duration-200\" />\r\n\r\n            {unreadCount > 0 && (\r\n              <div className=\"absolute -top-1.5 -right-1.5 md:-top-2 md:-right-2 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center z-20\">\r\n                <span className=\"text-white text-[10px] md:text-xs font-semibold leading-none\">\r\n                  {unreadCount > 99 ? '99+' : unreadCount}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-80 p-0\" align=\"end\">\r\n          <div className=\"p-4 border-b\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <h3 className=\"font-semibold\">Notifications</h3>\r\n              <div className=\"flex gap-2\">\r\n                {unreadCount > 0 && (\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleMarkAllAsRead}\r\n                    className=\"text-xs\"\r\n                  >\r\n                    Mark all read\r\n                  </Button>\r\n                )}\r\n                {notifications.length > 0 && unreadCount === 0 && (\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleRemoveAllClick}\r\n                    className=\"text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                  >\r\n                    Remove all\r\n                  </Button>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"h-80 overflow-y-auto\">\r\n            {loading ? (\r\n              <div className=\"p-4 text-center text-muted-foreground\">\r\n                Loading notifications...\r\n              </div>\r\n            ) : notifications.length === 0 ? (\r\n              <div className=\"p-4 text-center text-muted-foreground\">\r\n                No notifications yet\r\n              </div>\r\n            ) : (\r\n              <div className=\"divide-y\">\r\n                {Array.isArray(notifications) && notifications.map((notification) => (\r\n                  <div\r\n                    key={notification.id}\r\n                    className={`p-4 cursor-pointer hover:bg-muted/50 transition-colors ${!notification.isRead ? 'bg-blue-50/50' : ''\r\n                      }`}\r\n                    onClick={() => handleNotificationClick(notification)}\r\n                  >\r\n                    <div className=\"flex items-start gap-3\">\r\n                      <div className={`w-2 h-2 rounded-full mt-2 ${!notification.isRead ? 'bg-blue-500' : 'bg-gray-300'\r\n                        }`} />\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <p className=\"font-medium text-sm\">{notification.title}</p>\r\n                        <p className=\"text-sm text-muted-foreground mt-1\">\r\n                          {notification.message}\r\n                        </p>\r\n                        <p className=\"text-xs text-muted-foreground mt-2\">\r\n                          {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n          {safeNotifications.length > 0 && (\r\n            <div className=\"p-3 border-t bg-muted/30\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"w-full text-xs\"\r\n                onClick={() => {\r\n                  setIsOpen(false);\r\n                  router.push('/notifications');\r\n                }}\r\n              >\r\n                View All Notifications\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </PopoverContent>\r\n      </Popover>\r\n\r\n      {/* Delete Confirmation Dialog */}\r\n      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Remove All Notifications</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Are you sure you want to remove all notifications? This action cannot be undone.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleConfirmRemoveAll}\r\n              className=\"bg-red-600 hover:bg-red-700\"\r\n            >\r\n              Remove All\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAUA;AAaA;AACA;AACA;AApCA;;;;;;;;;;;AA0Ce,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,MAAM,OAAO,CAAC,iBAAiB,gBAAgB,EAAE;IAE3E,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,WAAW;YACX,IAAI;YACJ,IAAI;YAEJ,IAAI,aAAa,SAAS;gBACxB,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE,GAAG;gBACxC,QAAQ,MAAM,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD;YAClC,OAAO;gBACL,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,0BAAuB,AAAD,EAAE,GAAG;gBAC1C,QAAQ,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD;YACpC;YAEA,2CAA2C;YAC3C,MAAM,SAAS,QAAQ,iBAAiB,UAAU,EAAE;YACpD,iBAAiB,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;YACpD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,iBAAiB,EAAE;YACnB,eAAe;QACjB,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,4BAA4B;YAC5B,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD,EAAE,aAAa,EAAE;YACnD,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,gCAA6B,AAAD,EAAE,aAAa,EAAE;YACrD;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,aAAa,EAAE,GAAG;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,IAAI;YAGhE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;YAC1C,UAAU;YACV,IAAI,aAAa,IAAI,EAAE,eAAe,eAAe,aAAa,IAAI,EAAE,aAAa;gBACnF,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,WAAW;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,kCAA+B,AAAD;YACtC,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,oCAAiC,AAAD;YACxC;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,CAAC;YAE/C,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB;QAC3B,oBAAoB;IACtB;IAEA,MAAM,yBAAyB;QAC7B,oBAAoB;QAEpB,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD;YAClC,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,gCAA6B,AAAD;YACpC;YAEA,qBAAqB;YACrB,iBAAiB,EAAE;YACnB,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,MAAM,WAAW,YAAY,oBAAoB;QAEjD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAmB;IAEvB,qBACE;;0BACE,8OAAC,mIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACnC,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAEf,cAAc,mBACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;;0CACzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;gDAIF,cAAc,MAAM,GAAG,KAAK,gBAAgB,mBAC3C,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAI,WAAU;0CACZ,wBACC,8OAAC;oCAAI,WAAU;8CAAwC;;;;;2CAGrD,cAAc,MAAM,KAAK,kBAC3B,8OAAC;oCAAI,WAAU;8CAAwC;;;;;yDAIvD,8OAAC;oCAAI,WAAU;8CACZ,MAAM,OAAO,CAAC,kBAAkB,cAAc,GAAG,CAAC,CAAC,6BAClD,8OAAC;4CAEC,WAAW,CAAC,uDAAuD,EAAE,CAAC,aAAa,MAAM,GAAG,kBAAkB,IAC1G;4CACJ,SAAS,IAAM,wBAAwB;sDAEvC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,0BAA0B,EAAE,CAAC,aAAa,MAAM,GAAG,gBAAgB,eAChF;;;;;;kEACJ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAuB,aAAa,KAAK;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO;;;;;;0EAEvB,8OAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,SAAS,GAAG;oEAAE,WAAW;gEAAK;;;;;;;;;;;;;;;;;;2CAd1E,aAAa,EAAE;;;;;;;;;;;;;;;4BAuB7B,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,UAAU;wCACV,OAAO,IAAI,CAAC;oCACd;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACxF,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;QACjF,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/studentAuthServices.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { GoogleAuthData } from '@/lib/types';\r\n\r\ninterface StudentRegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface StudentLoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport const continueWithEmail = async (data: ContinueWithEmailData) => {\r\n  const response = await axiosInstance.post('/student/continue-with-email', data);\r\n  return response.data;\r\n};\r\n\r\nexport const registerStudent = async (data: StudentRegisterData) => {\r\n  const response = await axiosInstance.post('/student/register', data);\r\n  return response.data;\r\n};\r\n\r\nexport const loginStudent = async (data: StudentLoginData) => {\r\n  const response = await axiosInstance.post('/student/login', data);\r\n  return response.data;\r\n};\r\n\r\nexport const logoutStudent = async (): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.post('/student/logout');\r\n\r\n    localStorage.removeItem('studentToken');\r\n    localStorage.removeItem('student_data');\r\n\r\n    return response.data;\r\n  } catch {\r\n    localStorage.removeItem('studentToken');\r\n    localStorage.removeItem('student_data');\r\n\r\n    return {\r\n      success: true,\r\n      message: 'Logged out successfully',\r\n    };\r\n  }\r\n};\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/student/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/student/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport const googleAuthStudent = async (googleAuthData: GoogleAuthData) => {\r\n    const response = await axiosInstance.post(`/student/google-auth`, googleAuthData);\r\n    return response.data;\r\n};\r\n\r\nexport const studentverifyEmail = async (token: string) => {\r\n    const response = await axiosInstance.post(`/student/verify-email`, { token });\r\n    return response.data;\r\n};\r\n\r\nexport const studentresendVerificationEmail = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/resend-verification`, { email });\r\n    return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAgCO,MAAM,oBAAoB,OAAO;IACtC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,gCAAgC;IAC1E,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,qBAAqB;IAC/D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,kBAAkB;IAC5D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gBAAgB;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;QAE1C,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,OAAO,SAAS,IAAI;IACtB,EAAE,OAAM;QACN,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oBAAoB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE;IAClE,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,qBAAqB,OAAO;IACrC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE;QAAE;IAAM;IAC3E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,iCAAiC,OAAO;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAAE;IAAM;IAClF,OAAO,SAAS,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 1010, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/AuthService.ts"], "sourcesContent": ["import { axiosInstance } from '../lib/axios';\r\n\r\ninterface RegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface LoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  email?: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport async function continueWithEmail(data: ContinueWithEmailData) {\r\n  const response = await axiosInstance.post('/auth-client/continue-with-email', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function registerUser(data: RegisterData) {\r\n  const response = await axiosInstance.post('/auth-client/register', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function loginUser(data: LoginData) {\r\n  const response = await axiosInstance.post('/auth-client/login', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport function logoutUser(): void {\r\n  localStorage.removeItem('user');\r\n}\r\n\r\nexport const generateJWT = async (contact: string | undefined, password : string | undefined) => {\r\n  const response = await axiosInstance.post(`/auth-client/generate-jwt`, { contact, password });\r\n  return response.data;\r\n};\r\n\r\n\r\n\r\nexport const verifyEmail = async (token: string) => {\r\n  const response = await axiosInstance.get(`/auth-client/verify-email`, { params: { token } });\r\n  return response.data;\r\n};\r\n\r\nexport const resendVerificationEmail = async (email: string) => {\r\n  const response = await axiosInstance.post(`/auth-client/resend-verification`, { email });\r\n  return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AA+BO,eAAe,kBAAkB,IAA2B;IACjE,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,oCAAoC;IAC9E,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,aAAa,IAAkB;IACnD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,yBAAyB;IACnE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAe;IAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,sBAAsB;IAChE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,SAAS;IACd,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,cAAc,OAAO,SAA6B;IAC7D,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE;QAAS;IAAS;IAC3F,OAAO,SAAS,IAAI;AACtB;AAIO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE,QAAQ;YAAE;QAAM;IAAE;IAC1F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,gCAAgC,CAAC,EAAE;QAAE;IAAM;IACtF,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 1073, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/mockExamStreakApi.ts"], "sourcesContent": ["import { axiosInstance } from \"../lib/axios\";\r\n\r\nexport const saveMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/mock-exam-streak/${studentId}`, {}, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/mock-exam-streak/${studentId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data }; \r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\nexport const saveweeklyMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/mock-exam-weekly-streak/${studentId}`, {}, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getweeklyMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/mock-exam-weekly-streak/${studentId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data }; \r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE,CAAC,GAAG;YAC7E,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE;YACzE,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AACO,MAAM,2BAA2B,OAAO;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,WAAW,EAAE,CAAC,GAAG;YACpF,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,0BAA0B,OAAO;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,WAAW,EAAE;YAChF,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 1159, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/streakcountdisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { getMockExamStreak } from '@/services/mockExamStreakApi';\r\n\r\n\r\ninterface MockExamStreakResponse {\r\n  success: boolean;\r\n  data?: { streak: number; lastAttempt: string | null };\r\n  error?: string;\r\n}\r\n\r\ninterface StreakDisplayProps {\r\n  studentId?: string;\r\n}\r\n\r\nconst StreakDisplay: React.FC<StreakDisplayProps> = ({ studentId }) => {\r\n  const [streak, setStreak] = useState<number>(0);\r\n\r\n  useEffect(() => {\r\n    const fetchStreak = async () => {\r\n      if (!studentId) {\r\n        setStreak(0);\r\n        return;\r\n      }\r\n      const response: MockExamStreakResponse = await getMockExamStreak(studentId);\r\n      if (response.success && response.data) {\r\n        setStreak(response.data.streak || 0);\r\n      } else {\r\n        setStreak(0);\r\n      }\r\n    };\r\n    fetchStreak();\r\n  }, [studentId]);\r\n\r\n  return (\r\n       <span className=\"bg-black  text-white text-l rounded-full px-2 py-1 flex items-center gap-1\">\r\n      🔥 {streak}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StreakDisplay;"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAaA,MAAM,gBAA8C,CAAC,EAAE,SAAS,EAAE;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI,CAAC,WAAW;gBACd,UAAU;gBACV;YACF;YACA,MAAM,WAAmC,MAAM,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE;YACjE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,UAAU,SAAS,IAAI,CAAC,MAAM,IAAI;YACpC,OAAO;gBACL,UAAU;YACZ;QACF;QACA;IACF,GAAG;QAAC;KAAU;IAEd,qBACK,8OAAC;QAAK,WAAU;;YAA6E;YAC1F;;;;;;;AAGV;uCAEe", "debugId": null}}, {"offset": {"line": 1206, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/cartApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport interface CartItem {\r\n  id: string;\r\n  userId: string;\r\n  userType: 'STUDENT' | 'CLASS';\r\n  itemId: string;\r\n  quantity: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  item: {\r\n    id: string;\r\n    name: string;\r\n    coinPrice: number;\r\n    image: string | null;\r\n    availableStock: number;\r\n  };\r\n}\r\n\r\nexport interface CartTotal {\r\n  totalCoins: number;\r\n  totalItems: number;\r\n  itemCount: number;\r\n}\r\n\r\nexport interface ApiResponse<T> {\r\n  success: boolean;\r\n  data?: T;\r\n  error?: string;\r\n  message?: string;\r\n}\r\n\r\nexport const addToCart = async (itemId: string, quantity: number = 1): Promise<ApiResponse<CartItem>> => {\r\n  try {\r\n    const response = await axiosInstance.post('/cart/add', {\r\n      itemId,\r\n      quantity\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to add item to cart'\r\n    };\r\n  }\r\n};\r\n\r\n// Get all cart items\r\nexport const getCartItems = async (): Promise<ApiResponse<CartItem[]>> => {\r\n  try {\r\n    const response = await axiosInstance.get('/cart');\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to fetch cart items'\r\n    };\r\n  }\r\n};\r\n\r\n// Update cart item quantity\r\nexport const updateCartItemQuantity = async (itemId: string, quantity: number): Promise<ApiResponse<CartItem>> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/cart/item/${itemId}`, {\r\n      quantity\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to update cart item'\r\n    };\r\n  }\r\n};\r\n\r\n// Remove item from cart\r\nexport const removeFromCart = async (itemId: string): Promise<ApiResponse<null>> => {\r\n  try {\r\n    const response = await axiosInstance.delete(`/cart/item/${itemId}`);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to remove item from cart'\r\n    };\r\n  }\r\n};\r\n\r\n// Clear entire cart\r\nexport const clearCart = async (): Promise<ApiResponse<null>> => {\r\n  try {\r\n    const response = await axiosInstance.delete('/cart/clear');\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to clear cart'\r\n    };\r\n  }\r\n};\r\n\r\n// Get cart total\r\nexport const getCartTotal = async (): Promise<ApiResponse<CartTotal>> => {\r\n  try {\r\n    const response = await axiosInstance.get('/cart/total');\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to get cart total'\r\n    };\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAgCO,MAAM,YAAY,OAAO,QAAgB,WAAmB,CAAC;IAClE,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,aAAa;YACrD;YACA;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,yBAAyB,OAAO,QAAgB;IAC3D,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,EAAE;YAC/D;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,QAAQ;QAClE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,YAAY;IACvB,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;QAC5C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 1293, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/storePurchaseApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { PurchaseData, StoreOrder } from '@/lib/types';\r\n\r\nexport type { PurchaseData, StoreOrder };\r\n\r\nexport const purchaseItems = async (data: PurchaseData) => {\r\n  try {\r\n    const response = await axiosInstance.post('/store/purchase', data);\r\n    return {\r\n      success: true,\r\n      data: response.data.data\r\n    };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.message || error.message || 'Purchase failed'\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMyOrders = async () => {\r\n  try {\r\n    const response = await axiosInstance.get('/store/orders');\r\n    return {\r\n      success: true,\r\n      data: response.data.data\r\n    };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.message || error.message || 'Failed to fetch orders'\r\n    };\r\n  }\r\n};\r\n\r\nexport const getOrderDetails = async (orderId: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(`/store/orders/${orderId}`);\r\n    return {\r\n      success: true,\r\n      data: response.data.data\r\n    };\r\n  } catch (error: any) {\r\n    console.error('Failed to fetch order details:', error);\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.message || error.message || 'Failed to fetch order details'\r\n    };\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAKO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,mBAAmB;QAC7D,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;QAC3D;IACF;AACF;AAEO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;QAC3D;IACF;AACF;AAEO,MAAM,kBAAkB,OAAO;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,SAAS;QACnE,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;QAC3D;IACF;AACF", "debugId": null}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\r\nimport { XIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Dialog({ ...props }: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />;\r\n}\r\n\r\nfunction DialogTrigger({ ...props }: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />;\r\n}\r\n\r\nfunction DialogPortal({ ...props }: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />;\r\n}\r\n\r\nfunction DialogClose({ ...props }: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />;\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  );\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogTitle({ className, ...props }: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn('text-lg leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EAAE,GAAG,OAA4D;IACrF,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;QACvE,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1522, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1784, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport {\r\n  Menu,\r\n  X,\r\n  User,\r\n  ShoppingBag,\r\n  Share2,\r\n  UserCircle,\r\n  LayoutDashboard,\r\n  MessageSquare,\r\n  Coins,\r\n  BadgeCent,\r\n  ShoppingCart,\r\n  Plus,\r\n  Minus,\r\n  CreditCard,\r\n  ChevronDown,\r\n  ChevronUp,\r\n} from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/store\";\r\nimport { useAppDispatch } from \"@/store/hooks\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { isStudentAuthenticated, clearStudentAuthToken } from \"@/lib/utils\";\r\nimport ProfileCompletionIndicator from \"./ProfileCompletionIndicator\";\r\nimport NotificationBell from \"./NotificationBell\";\r\nimport { Avatar, AvatarFallback } from \"@/components/ui/avatar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { toast } from \"sonner\";\r\nimport { logoutStudent } from \"@/services/studentAuthServices\";\r\nimport { clearUser } from \"@/store/slices/userSlice\";\r\nimport { clearStudentProfileData } from \"@/store/slices/studentProfileSlice\";\r\nimport { fetchStudentProfile } from \"@/store/thunks/studentProfileThunks\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { useMotionValue, useAnimationFrame } from \"framer-motion\";\r\nimport { generateJWT } from \"@/services/AuthService\";\r\nimport StreakDisplay from \"@/components/ui/streakcountdisplay\";\r\nimport * as cartApi from '@/services/cartApi';\r\nimport * as storePurchaseApi from '@/services/storePurchaseApi';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\";\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated, user } = useSelector(\r\n    (state: RootState) => state.user\r\n  );\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);\r\n  const [studentData, setStudentData] = useState<any>(null);\r\n  const [classStatus, setClassStatus] = useState<string | null>(null);\r\n  const [cart, setCart] = useState<cartApi.CartItem[]>([]);\r\n  const [showCart, setShowCart] = useState(false);\r\n  const [isCheckingOut, setIsCheckingOut] = useState(false);\r\n  const [quizDropdownOpen, setQuizDropdownOpen] = useState(false);\r\n  const dispatch = useAppDispatch();\r\n  const router = useRouter();\r\n\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const [contentWidth, setContentWidth] = useState(0);\r\n  const x = useMotionValue(0);\r\n  const speed = contentWidth / 20;\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n\r\n  const loadCartItems = async () => {\r\n    try {\r\n      const result = await cartApi.getCartItems();\r\n      if (result.success && result.data) {\r\n        setCart(result.data);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading cart:', error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const isLoggedIn = isStudentAuthenticated();\r\n    setIsStudentLoggedIn(isLoggedIn);\r\n\r\n    if (isLoggedIn) {\r\n      const storedData = localStorage.getItem(\"student_data\");\r\n      if (storedData) {\r\n        setStudentData(JSON.parse(storedData));\r\n      }\r\n      dispatch(fetchStudentProfile());\r\n      loadCartItems();\r\n    }\r\n\r\n    if (isAuthenticated) {\r\n      loadCartItems();\r\n    }\r\n    const fetchClassStatus = async () => {\r\n      if (isAuthenticated && user?.id) {\r\n        try {\r\n          const response = await axiosInstance.get(`/classes/details/${user.id}`);\r\n          if (response.data && response.data.status) {\r\n            setClassStatus(response.data.status.status);\r\n          }\r\n        } catch (error) {\r\n          console.error('Error fetching class status:', error);\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchClassStatus();\r\n\r\n    const handleStorageChange = () => {\r\n      const newLoginStatus = isStudentAuthenticated();\r\n      setIsStudentLoggedIn(newLoginStatus);\r\n      if (newLoginStatus) {\r\n        const storedData = localStorage.getItem(\"student_data\");\r\n        if (storedData) {\r\n          setStudentData(JSON.parse(storedData));\r\n        }\r\n        dispatch(fetchStudentProfile());\r\n        loadCartItems();\r\n      } else {\r\n        setStudentData(null);\r\n        setCart([]);\r\n      }\r\n    };\r\n\r\n    const handleCartUpdate = () => {\r\n      if (isLoggedIn || isAuthenticated) {\r\n        loadCartItems();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"storage\", handleStorageChange);\r\n    window.addEventListener(\"cartUpdated\", handleCartUpdate);\r\n\r\n    if (contentRef.current) {\r\n      const width = contentRef.current.getBoundingClientRect().width;\r\n      setContentWidth(width);\r\n    }\r\n\r\n    return () => {\r\n      window.removeEventListener(\"storage\", handleStorageChange);\r\n      window.removeEventListener(\"cartUpdated\", handleCartUpdate);\r\n    };\r\n  }, [dispatch]);\r\n\r\n  useAnimationFrame((_, delta) => {\r\n    if (contentWidth === 0) return;\r\n    const currentX = x.get();\r\n    const deltaX = (speed * delta) / 1000;\r\n    let newX = currentX - deltaX;\r\n    if (newX <= -contentWidth) {\r\n      newX = 0;\r\n    }\r\n    x.set(newX);\r\n  });\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n  const toggleQuizDropdown = () => setQuizDropdownOpen(!quizDropdownOpen);\r\n\r\n  const handleStudentLogout = async () => {\r\n    try {\r\n      const response = await logoutStudent();\r\n      if (response.success !== false) {\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem(\"student_data\");\r\n        dispatch(clearStudentProfileData());\r\n        toast.success(\"Logged out successfully\");\r\n        window.dispatchEvent(new Event(\"storage\"));\r\n      } else {\r\n        toast.error(response.message || \"Failed to logout\");\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem(\"student_data\");\r\n        dispatch(clearStudentProfileData());\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Failed to logout\", error);\r\n      toast.error(\"Failed to logout\");\r\n      localStorage.removeItem(\"student_data\");\r\n      clearStudentAuthToken();\r\n      setIsStudentLoggedIn(false);\r\n      setStudentData(null);\r\n      dispatch(clearStudentProfileData());\r\n    }\r\n  };\r\n\r\n  const removeFromCart = async (productId: string) => {\r\n    try {\r\n      const result = await cartApi.removeFromCart(productId);\r\n      if (result.success) {\r\n        await loadCartItems();\r\n        toast.success(\"Item removed from cart!\");\r\n      } else {\r\n        toast.error(result.error || \"Failed to remove item from cart\");\r\n      }\r\n    } catch (error) {\r\n      console.error('Error removing from cart:', error);\r\n      toast.error(\"Failed to remove item from cart\");\r\n    }\r\n  };\r\n\r\n  const updateCartQuantity = async (productId: string, newQuantity: number) => {\r\n    try {\r\n      const cartItem = cart.find(item => item.itemId === productId);\r\n\r\n      if (cartItem && newQuantity > cartItem.item.availableStock) {\r\n        toast.error(`Only ${cartItem.item.availableStock} items available in stock`);\r\n        return;\r\n      }\r\n\r\n      const result = await cartApi.updateCartItemQuantity(productId, newQuantity);\r\n      if (result.success) {\r\n        await loadCartItems();\r\n        if (newQuantity === 0) {\r\n          toast.success(\"Item removed from cart!\");\r\n        }\r\n      } else {\r\n        const errorMessage = result.error || \"Failed to update cart item\";\r\n        if (errorMessage.includes(\"stock\") || errorMessage.includes(\"available\")) {\r\n          toast.error(\"Item is out of stock or insufficient quantity available\");\r\n        } else {\r\n          toast.error(errorMessage);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating cart:', error);\r\n      toast.error(\"Failed to update cart item\");\r\n    }\r\n  };\r\n\r\n  const getTotalCartPrice = () => {\r\n    return cart.reduce((total, item) => {\r\n      return total + (item.item.coinPrice * item.quantity);\r\n    }, 0);\r\n  };\r\n\r\n  const handleCheckout = async () => {\r\n    if (cart.length === 0) {\r\n      toast.error(\"Your cart is empty\");\r\n      return;\r\n    }\r\n\r\n    if (!isStudentLoggedIn && !isAuthenticated) {\r\n      toast.error(\"Please login to checkout\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsCheckingOut(true);\r\n\r\n      const cartItems = cart.map(item => ({\r\n        id: item.itemId,\r\n        name: item.item.name,\r\n        coinPrice: item.item.coinPrice,\r\n        quantity: item.quantity,\r\n        image: item.item.image || ''\r\n      }));\r\n\r\n      const totalCoins = getTotalCartPrice();\r\n\r\n      const purchaseData: storePurchaseApi.PurchaseData = {\r\n        cartItems,\r\n        totalCoins\r\n      };\r\n\r\n      const result = await storePurchaseApi.purchaseItems(purchaseData);\r\n\r\n      if (!result.success) {\r\n        if (result.error === 'PROFILE_NOT_APPROVED') {\r\n          const errorMessage = result.data?.message || 'Your profile is not approved yet. Please complete your profile and wait for admin approval.';\r\n          toast.error(errorMessage);\r\n          return;\r\n        }\r\n        throw new Error(result.error);\r\n      }\r\n\r\n      await cartApi.clearCart();\r\n      await loadCartItems();\r\n\r\n      toast.success('Purchase completed successfully!');\r\n      setShowCart(false);\r\n\r\n      if (isStudentLoggedIn) {\r\n        router.push('/student/my-orders');\r\n      } else {\r\n        router.push('/classes/my-orders');\r\n      }\r\n\r\n    } catch (error: any) {\r\n      console.error('Checkout error:', error);\r\n      toast.error(error.message || 'Checkout failed. Please try again.');\r\n    } finally {\r\n      setIsCheckingOut(false);\r\n    }\r\n  };\r\n\r\n  const accessClassDashboard = async () => {\r\n    try {\r\n      const response = await generateJWT(user?.contactNo, user?.password);\r\n\r\n      if (response.success) {\r\n        const { token } = response.data;\r\n        const redirectUrl = `${process.env.NEXT_PUBLIC_RANNDASS_URL}/login-class-link?uid=${user?.id}&token=${token}`;\r\n        window.location.href = redirectUrl;\r\n      } else {\r\n        toast.error(response.message || \"Failed to generate token\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to generate token\", error);\r\n      toast.error(\"Failed to generate token\");\r\n    }\r\n  };\r\n\r\n  type NavLink = {\r\n    href?: string;\r\n    label: React.ReactNode;\r\n    dropdown?: { href: string; label: string; isNew?: boolean }[];\r\n    isNew?: boolean;\r\n  };\r\n\r\n  const navLinks: NavLink[] = [\r\n    { href: \"/verified-classes\", label: \"Find Tutor\" },\r\n    {\r\n      label: (\r\n        <div className=\"flex items-center gap-2\">\r\n          <span>Quiz</span>\r\n          <StreakDisplay studentId={studentData?.id} />\r\n        </div>\r\n      ),\r\n      dropdown: [\r\n        { href: \"/mock-exam-card\", label: \"Daily Quiz\" },\r\n        { href: \"/weekly-exam-card\", label: \"Weekly Quiz\" },\r\n        { href: \"/uwhiz\", label: \"Uwhiz\" },\r\n      ]\r\n    },\r\n    { href: \"/store\", label: \"Store\" },\r\n    ...(isStudentLoggedIn\r\n      ? [\r\n        { href: \"/student/pricing\", label: \"Pricing\" },\r\n      ]\r\n      : []),\r\n    ...(!isStudentLoggedIn\r\n      ? [\r\n        { href: \"/careers\", label: \"Career\" },\r\n      ]\r\n      : []),\r\n  ];\r\n\r\n  const classMenuItems = [\r\n    {\r\n      href: \"/classes/profile\",\r\n      icon: <UserCircle className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Profile\",\r\n    },\r\n    {\r\n      href: \"/classes/chat\",\r\n      icon: <MessageSquare className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Messages\",\r\n    },\r\n    {\r\n      href: \"/coins\",\r\n      icon: <Coins className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Coins\",\r\n    },\r\n    {\r\n      href: \"/classes/my-orders\",\r\n      icon: <ShoppingBag className=\"w-5 h-5 mr-2\" />,\r\n      label: \"My Orders\",\r\n    },\r\n    {\r\n      onClick: accessClassDashboard,\r\n      icon: <LayoutDashboard className=\"w-5 h-5 mr-2\" />,\r\n      label: \"My Dashboard\",\r\n    },\r\n    {\r\n      href: \"/classes/referral-dashboard\",\r\n      icon: <Share2 className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Referral Dashboard\",\r\n    },\r\n    ...(classStatus === 'APPROVED' ? [{\r\n      href: \"/classes/payment\",\r\n      icon: <BadgeCent className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Payment Details\",\r\n    }] : [])\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <header className=\"sticky top-0 z-50 w-full bg-black\">\r\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex h-16 items-center justify-between\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"flex items-center space-x-2 transition-transform hover:scale-105\"\r\n            >\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Preply Logo\"\r\n                width={120}\r\n                height={40}\r\n                className=\"rounded-sm\"\r\n              />\r\n            </Link>\r\n            <nav className=\"hidden md:flex items-center space-x-6\">\r\n              {navLinks.map((link, idx) =>\r\n                link.dropdown ? (\r\n                  <DropdownMenu key={idx} open={dropdownOpen} onOpenChange={setDropdownOpen}>\r\n                    <DropdownMenuTrigger asChild>\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        onClick={() => setDropdownOpen((prev) => !prev)}\r\n                        className=\"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400 bg-transparent hover:bg-transparent\"\r\n                      >\r\n                        {link.label}\r\n                        {dropdownOpen ? (\r\n                          <ChevronUp className=\"w-4 h-4 ml-1\" />\r\n                        ) : (\r\n                          <ChevronDown className=\"w-4 h-4 ml-1\" />\r\n                        )}\r\n                      </Button>\r\n                    </DropdownMenuTrigger>\r\n\r\n                    <DropdownMenuContent className=\"w-48 p-0 bg-black border border-gray-700 rounded shadow-lg\">\r\n                      {link.dropdown.map((item: any) => (\r\n                        <DropdownMenuItem\r\n                          key={item.href}\r\n                          asChild\r\n                          onClick={() => setDropdownOpen(false)}\r\n                        >\r\n                          <Link\r\n                            href={item.href}\r\n                            className=\"flex items-center w-full px-4 py-2 text-sm text-white hover:text-orange-400 transition\"\r\n                          >\r\n                            {item.label}\r\n                            {item.isNew && (\r\n                              <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse\">\r\n                                Trending\r\n                              </span>\r\n                            )}\r\n                          </Link>\r\n                        </DropdownMenuItem>\r\n                      ))}\r\n                    </DropdownMenuContent>\r\n                  </DropdownMenu>\r\n                ) : (\r\n                  <Link\r\n                    key={link.href}\r\n                    href={link.href as string}\r\n                    className=\"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400\"\r\n                  >\r\n                    {link.label}\r\n                    {link.isNew && (\r\n                      <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse\">\r\n                        Trending\r\n                      </span>\r\n                    )}\r\n                  </Link>\r\n                )\r\n              )}\r\n            </nav>\r\n\r\n            <div className=\"flex items-center space-x-3\">\r\n              {isAuthenticated || isStudentLoggedIn ? (\r\n                <>\r\n                  <NotificationBell\r\n                    userType={isAuthenticated ? \"class\" : \"student\"}\r\n                  />\r\n                  {cart.length > 0 && (\r\n                    <div className=\"relative\">\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        size=\"icon\"\r\n                        onClick={() => setShowCart(true)}\r\n                        className=\"text-white hover:bg-gray-800 rounded-full relative\"\r\n                      >\r\n                        <ShoppingCart className=\"h-5 w-5\" />\r\n\r\n                        <span className=\"absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\r\n                          {cart.reduce((total, item) => total + item.quantity, 0)}\r\n                        </span>\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                  <Popover>\r\n                    <PopoverTrigger asChild>\r\n                      <Avatar className=\"cursor-pointer h-9 w-9 hover:opacity-80 transition-opacity\">\r\n                        <AvatarFallback className=\"bg-white text-black flex items-center justify-center text-sm font-semibold\">\r\n                          {isAuthenticated\r\n                            ? user?.firstName && user?.lastName\r\n                              ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                              : \"CT\"\r\n                            : studentData?.firstName && studentData?.lastName\r\n                              ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                              : \"ST\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                    </PopoverTrigger>\r\n                    <PopoverContent className=\"w-64 bg-white p-4 rounded-lg shadow-lg\">\r\n                      <div className=\"flex items-center gap-3 mb-4\">\r\n                        <Avatar className=\"h-10 w-10\">\r\n                          <AvatarFallback className=\"bg-white text-black\">\r\n                            {isAuthenticated\r\n                              ? user?.firstName && user?.lastName\r\n                                ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                                : \"CT\"\r\n                              : studentData?.firstName && studentData?.lastName\r\n                                ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                                : \"ST\"}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div>\r\n                          <p className=\"font-medium text-black\">\r\n                            {isAuthenticated\r\n                              ? user?.firstName && user?.lastName\r\n                                ? `${user.firstName} ${user.lastName}`\r\n                                : user?.className || \"Class Account\"\r\n                              : studentData?.firstName && studentData?.lastName\r\n                                ? `${studentData.firstName} ${studentData.lastName}`\r\n                                : \"Student Account\"}\r\n                          </p>\r\n                          <p className=\"text-xs text-gray-600\">\r\n                            {isAuthenticated\r\n                              ? user?.contactNo || \"<EMAIL>\"\r\n                              : studentData?.contactNo || \"<EMAIL>\"}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        {isAuthenticated ? (\r\n                          <>\r\n                            {classMenuItems.map((item) => (\r\n                              <Button\r\n                                asChild\r\n                                variant=\"ghost\"\r\n                                className=\"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground\"\r\n                                key={item.href || item.label}\r\n                              >\r\n                                {item.href ? (\r\n                                  <Link\r\n                                    href={item.href}\r\n                                    className=\"flex items-center\"\r\n                                  >\r\n                                    {item.icon}\r\n                                    <span>{item.label}</span>\r\n                                  </Link>\r\n                                ) : (\r\n                                  <div\r\n                                    onClick={item.onClick}\r\n                                    className=\"flex items-center w-full\"\r\n                                  >\r\n                                    {item.icon}\r\n                                    <span>{item.label}</span>\r\n                                  </div>\r\n                                )}\r\n                              </Button>\r\n                            ))}\r\n                            <Button\r\n                              variant=\"ghost\"\r\n                              className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                              onClick={async () => {\r\n                                try {\r\n                                  const response = await axiosInstance.post(\r\n                                    \"/auth-client/logout\",\r\n                                    {}\r\n                                  );\r\n                                  if (response.data.success) {\r\n                                    router.push(\"/\");\r\n                                    dispatch(clearUser());\r\n                                    localStorage.removeItem(\"token\");\r\n                                    toast.success(\"Logged out successfully\");\r\n                                  }\r\n                                } catch (error) {\r\n                                  console.error(\"Logout error:\", error);\r\n                                  toast.error(\"Failed to logout\");\r\n                                }\r\n                              }}\r\n                            >\r\n                              <User className=\"w-5 h-5 mr-2\" />\r\n                              <span>Logout</span>\r\n                            </Button>\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <div className=\"space-y-2\">\r\n                              {[\r\n                                {\r\n                                  href: \"/student/profile\",\r\n                                  icon: <UserCircle className=\"w-5 h-5 mr-2\" />,\r\n                                  label: \"Profile\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/chat\",\r\n                                  icon: (\r\n                                    <MessageSquare className=\"w-5 h-5 mr-2\" />\r\n                                  ),\r\n                                  label: \"Messages\",\r\n                                },\r\n                                {\r\n                                  href: \"/coins\",\r\n                                  icon: <Coins className=\"w-5 h-5 mr-2\" />,\r\n                                  label: \"Coins\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/wishlist\",\r\n                                  icon: (\r\n                                    <ShoppingBag className=\"w-5 h-5 mr-2\" />\r\n                                  ),\r\n                                  label: \"My Wishlist\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/referral-dashboard\",\r\n                                  icon: <Share2 className=\"w-5 h-5 mr-2\" />,\r\n                                  label: \"Referral Dashboard\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/my-orders\",\r\n                                  icon: (\r\n                                    <ShoppingBag className=\"w-5 h-5 mr-2\" />\r\n                                  ),\r\n                                  label: \"My Orders\",\r\n                                },\r\n                              ].map((item) => (\r\n                                <Button\r\n                                  asChild\r\n                                  variant=\"ghost\"\r\n                                  className=\"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground\"\r\n                                  key={item.href}\r\n                                >\r\n                                  <Link\r\n                                    href={item.href}\r\n                                    className=\"flex items-center\"\r\n                                  >\r\n                                    {item.icon}\r\n                                    <span>{item.label}</span>\r\n                                  </Link>\r\n                                </Button>\r\n                              ))}\r\n                              <Button\r\n                                onClick={handleStudentLogout}\r\n                                className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                              >\r\n                                <User className=\"w-5 h-5 mr-2\" />\r\n                                <span>Logout</span>\r\n                              </Button>\r\n                            </div>\r\n                          </>\r\n                        )}\r\n                      </div>\r\n                    </PopoverContent>\r\n                  </Popover>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <div className=\"hidden md:flex items-center gap-2\">\r\n                    <Button\r\n                      className=\"bg-[#ff914d] hover:bg-[#E88143] text-white text-sm px-4 py-2 rounded-md\"\r\n                      asChild\r\n                    >\r\n                      <Link href=\"/class/login\">Join as Tutor</Link>\r\n                    </Button>\r\n\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      className=\"bg-black text-white text-sm px-4 py-2 rounded-md border border-gray-700\"\r\n                      asChild\r\n                    >\r\n                      <Link href=\"/student/login\">Student Login</Link>\r\n                    </Button>\r\n                  </div>\r\n                </>\r\n              )}\r\n\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"md:hidden text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n                onClick={toggleMenu}\r\n              >\r\n                {isMenuOpen ? (\r\n                  <X className=\"h-6 w-6\" />\r\n                ) : (\r\n                  <Menu className=\"h-6 w-6\" />\r\n                )}\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      <div>\r\n        <div\r\n          className={`fixed inset-y-0 right-0 z-50 w-80 bg-black transform transition-all duration-300 ease-in-out md:hidden ${isMenuOpen ? \"translate-x-0\" : \"translate-x-full\"\r\n            }`}\r\n        >\r\n          <div className=\"flex flex-col h-full p-6\">\r\n            <div className=\"flex items-center justify-between mb-6\">\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Uest Logo\"\r\n                width={100}\r\n                height={32}\r\n                className=\"rounded-sm\"\r\n              />\r\n              <div className=\"flex items-center gap-2\">\r\n                {(isAuthenticated || isStudentLoggedIn) && (\r\n                  <div className=\"relative\">\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"icon\"\r\n                      onClick={() => {\r\n                        setShowCart(true);\r\n                        toggleMenu();\r\n                      }}\r\n                      className=\"text-orange-400 hover:bg-orange-500/10 rounded-full relative\"\r\n                    >\r\n                      <ShoppingCart className=\"h-5 w-5\" />\r\n                      {cart.length > 0 && (\r\n                        <span className=\"absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\r\n                          {cart.reduce((total, item) => total + item.quantity, 0)}\r\n                        </span>\r\n                      )}\r\n                    </Button>\r\n                  </div>\r\n                )}\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <X className=\"h-6 w-6\" />\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            {(isAuthenticated || isStudentLoggedIn) && (\r\n              <div className=\"mb-6\">\r\n                <div className=\"flex items-center gap-3 p-3 bg-gray-900 rounded-lg\">\r\n                  <Avatar className=\"h-10 w-10\">\r\n                    <AvatarFallback className=\"bg-white text-black\">\r\n                      {isAuthenticated\r\n                        ? user?.firstName && user?.lastName\r\n                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                          : \"CT\"\r\n                        : studentData?.firstName && studentData?.lastName\r\n                          ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                          : \"ST\"}\r\n                    </AvatarFallback>\r\n                  </Avatar>\r\n                  <div>\r\n                    <p className=\"font-medium text-white\">\r\n                      {isAuthenticated\r\n                        ? user?.firstName && user?.lastName\r\n                          ? `${user.firstName} ${user.lastName}`\r\n                          : user?.className || \"Class Account\"\r\n                        : studentData?.firstName && studentData?.lastName\r\n                          ? `${studentData.firstName} ${studentData.lastName}`\r\n                          : \"Student Account\"}\r\n                    </p>\r\n                    <p className=\"text-xs text-gray-400\">\r\n                      {isAuthenticated\r\n                        ? user?.contactNo || \"<EMAIL>\"\r\n                        : studentData?.contactNo || \"<EMAIL>\"}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <nav className=\"flex flex-col space-y-2\">\r\n              {navLinks.map((link) =>\r\n                link.dropdown ? (\r\n                  <div key={link.label?.toString()} className=\"group\">\r\n                    <button\r\n                      className=\"flex items-center justify-between w-full gap-3 px-4 py-3 text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-gray-900 rounded-md border border-gray-700 transition-colors\"\r\n                      onClick={toggleQuizDropdown}\r\n                    >\r\n                      <span className=\"flex items-center gap-2\">\r\n                        {typeof link.label === \"string\" ? link.label : link.label}\r\n                      </span>\r\n                      {quizDropdownOpen ? (\r\n                        <ChevronUp className=\"w-4 h-4 ml-1\" />\r\n                      ) : (\r\n                        <ChevronDown className=\"w-4 h-4 ml-1\" />\r\n                      )}\r\n                    </button>\r\n                    {quizDropdownOpen && (\r\n                      <div className=\"flex flex-col mt-1 ml-4 border-l border-gray-700\">\r\n                        {link.dropdown.map((item) => (\r\n                          <Link\r\n                            key={item.href}\r\n                            href={item.href}\r\n                            className=\"flex items-center px-4 py-2 text-sm text-gray-300 hover:text-orange-400 rounded transition\"\r\n                            onClick={toggleMenu}\r\n                          >\r\n                            {item.label}\r\n                            {item.isNew && (\r\n                              <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white\">\r\n                                Trending\r\n                              </span>\r\n                            )}\r\n                          </Link>\r\n                        ))}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                ) : (\r\n                  <Link\r\n                    key={link.href}\r\n                    href={link.href as string}\r\n                    className=\"flex items-center justify-between gap-3 px-4 py-3 text-base font-medium text-gray-300 hover:text-orange-400  rounded-md border border-gray-700 transition-colors\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <div className=\"flex items-center gap-3\">\r\n                      {typeof link.label === \"string\" ? link.label : link.label}\r\n                    </div>\r\n                    {link.isNew && (\r\n                      <span className=\"text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white\">\r\n                        Trending\r\n                      </span>\r\n                    )}\r\n                  </Link>\r\n                )\r\n              )}\r\n            </nav>\r\n\r\n            <div className=\"mt-auto space-y-2\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                    onClick={async () => {\r\n                      try {\r\n                        const response = await axiosInstance.post(\r\n                          \"/auth-client/logout\",\r\n                          {}\r\n                        );\r\n                        if (response.data.success) {\r\n                          router.push(\"/\");\r\n                          dispatch(clearUser());\r\n                          localStorage.removeItem(\"token\");\r\n                          toast.success(\"Logged out successfully\");\r\n                        }\r\n                      } catch (error) {\r\n                        console.error(\"Logout error:\", error);\r\n                        toast.error(\"Failed to logout\");\r\n                      }\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <User className=\"w-5 h-5 mr-2\" />\r\n                    <span>Logout</span>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                    onClick={() => {\r\n                      handleStudentLogout();\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <User className=\"w-5 h-5 mr-2\" />\r\n                    <span>Logout</span>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <div className=\"space-y-2\">\r\n                  <Button\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#E88143] text-white rounded-lg py-3\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/class/login\" onClick={toggleMenu}>\r\n                      Tutor Login\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"w-full text-[#ff914d] hover:bg-gray-900 rounded-lg py-3 border border-gray-700\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/student/login\" onClick={toggleMenu}>\r\n                      Student Login\r\n                    </Link>\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {isStudentLoggedIn && <ProfileCompletionIndicator />}\r\n      </div>\r\n\r\n      {/* Shopping Cart Dialog */}\r\n      <Dialog open={showCart} onOpenChange={setShowCart}>\r\n        <DialogContent className=\"max-w-2xl max-h-[80vh] overflow-y-auto\">\r\n          <DialogHeader>\r\n            <DialogTitle className=\"flex items-center gap-2\">\r\n              <ShoppingCart className=\"w-5 h-5\" />\r\n              Shopping Cart ({cart.reduce((total, item) => total + item.quantity, 0)} items)\r\n            </DialogTitle>\r\n            <DialogDescription>\r\n              Review your items before checkout\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n\r\n          <div className=\"space-y-4\">\r\n            {cart.length === 0 ? (\r\n              <div className=\"text-center py-8\">\r\n                <ShoppingCart className=\"w-12 h-12 mx-auto text-muted-foreground mb-4\" />\r\n                <p className=\"text-muted-foreground\">Your cart is empty</p>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                {cart.map((item) => (\r\n                  <div key={item.id} className=\"flex items-center gap-4 p-4 border rounded-lg bg-card\">\r\n                    <Image\r\n                      src={\r\n                        item.item.image?.startsWith('http')\r\n                          ? item.item.image\r\n                          : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${item.item.image?.startsWith('/') ? item.item.image.substring(1) : item.item.image || 'uploads/store/placeholder.jpg'}`\r\n                      }\r\n                      alt={item.item.name}\r\n                      width={60}\r\n                      height={60}\r\n                      className=\"rounded object-cover\"\r\n                      onError={(e) => {\r\n                        const target = e.target as HTMLImageElement;\r\n                        target.src = \"/logo.png\";\r\n                      }}\r\n                    />\r\n                    <div className=\"flex-1\">\r\n                      <h4 className=\"font-medium text-card-foreground\">{item.item.name}</h4>\r\n                      <p className=\"text-orange-500 font-semibold flex items-center\">\r\n                        <Coins className=\"w-4 h-4 mr-1\" />\r\n                        {item.item.coinPrice} coins\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"outline\"\r\n                        onClick={() => updateCartQuantity(item.itemId, item.quantity - 1)}\r\n                      >\r\n                        <Minus className=\"w-3 h-3\" />\r\n                      </Button>\r\n                      <span className=\"w-8 text-center\">{item.quantity}</span>\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"outline\"\r\n                        onClick={() => updateCartQuantity(item.itemId, item.quantity + 1)}\r\n                      >\r\n                        <Plus className=\"w-3 h-3\" />\r\n                      </Button>\r\n                    </div>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"destructive\"\r\n                      onClick={() => removeFromCart(item.itemId)}\r\n                    >\r\n                      Remove\r\n                    </Button>\r\n                  </div>\r\n                ))}\r\n\r\n                <div className=\"border-t pt-4\">\r\n                  <div className=\"flex justify-between items-center text-lg font-semibold\">\r\n                    <span>Total:</span>\r\n                    <span className=\"text-orange-500 flex items-center\">\r\n                      <Coins className=\"w-5 h-5 mr-1\" />\r\n                      {getTotalCartPrice()} coins\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowCart(false)}>\r\n              Continue Shopping\r\n            </Button>\r\n            {cart.length > 0 && (\r\n              <Button\r\n                onClick={handleCheckout}\r\n                disabled={isCheckingOut}\r\n                className=\"bg-orange-500 hover:bg-orange-600 disabled:opacity-50\"\r\n              >\r\n                {isCheckingOut ? (\r\n                  <>\r\n                    <div className=\"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\" />\r\n                    Processing...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <CreditCard className=\"w-4 h-4 mr-2\" />\r\n                    Checkout ({getTotalCartPrice()} coins)\r\n                  </>\r\n                )}\r\n              </Button>\r\n            )}\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAQA;AAxDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DA,MAAM,SAAS;IACb,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAC1C,CAAC,QAAqB,MAAM,IAAI;IAElC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,QAAQ,eAAe;IAC7B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0HAAA,CAAA,eAAoB,AAAD;YACxC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,QAAQ,OAAO,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;QACxC,qBAAqB;QAErB,IAAI,YAAY;YACd,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,eAAe,KAAK,KAAK,CAAC;YAC5B;YACA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;YAC3B;QACF;QAEA,IAAI,iBAAiB;YACnB;QACF;QACA,MAAM,mBAAmB;YACvB,IAAI,mBAAmB,MAAM,IAAI;gBAC/B,IAAI;oBACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,KAAK,EAAE,EAAE;oBACtE,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE;wBACzC,eAAe,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM;oBAC5C;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAChD;YACF;QACF;QAEA;QAEA,MAAM,sBAAsB;YAC1B,MAAM,iBAAiB,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;YAC5C,qBAAqB;YACrB,IAAI,gBAAgB;gBAClB,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,eAAe,KAAK,KAAK,CAAC;gBAC5B;gBACA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;gBAC3B;YACF,OAAO;gBACL,eAAe;gBACf,QAAQ,EAAE;YACZ;QACF;QAEA,MAAM,mBAAmB;YACvB,IAAI,cAAc,iBAAiB;gBACjC;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,gBAAgB,CAAC,eAAe;QAEvC,IAAI,WAAW,OAAO,EAAE;YACtB,MAAM,QAAQ,WAAW,OAAO,CAAC,qBAAqB,GAAG,KAAK;YAC9D,gBAAgB;QAClB;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;YACtC,OAAO,mBAAmB,CAAC,eAAe;QAC5C;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qLAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC,GAAG;QACpB,IAAI,iBAAiB,GAAG;QACxB,MAAM,WAAW,EAAE,GAAG;QACtB,MAAM,SAAS,AAAC,QAAQ,QAAS;QACjC,IAAI,OAAO,WAAW;QACtB,IAAI,QAAQ,CAAC,cAAc;YACzB,OAAO;QACT;QACA,EAAE,GAAG,CAAC;IACR;IAEA,MAAM,aAAa,IAAM,cAAc,CAAC;IACxC,MAAM,qBAAqB,IAAM,oBAAoB,CAAC;IAEtD,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;YACnC,IAAI,SAAS,OAAO,KAAK,OAAO;gBAC9B,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;gBAC/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,aAAa,CAAC,IAAI,MAAM;YACjC,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,oBAAoB;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,aAAa,UAAU,CAAC;YACxB,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;YACpB,qBAAqB;YACrB,eAAe;YACf,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;QACjC;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0HAAA,CAAA,iBAAsB,AAAD,EAAE;YAC5C,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;gBACN,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,qBAAqB,OAAO,WAAmB;QACnD,IAAI;YACF,MAAM,WAAW,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;YAEnD,IAAI,YAAY,cAAc,SAAS,IAAI,CAAC,cAAc,EAAE;gBAC1D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC;gBAC3E;YACF;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,0HAAA,CAAA,yBAA8B,AAAD,EAAE,WAAW;YAC/D,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;gBACN,IAAI,gBAAgB,GAAG;oBACrB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF,OAAO;gBACL,MAAM,eAAe,OAAO,KAAK,IAAI;gBACrC,IAAI,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,cAAc;oBACxE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAO,KAAK,MAAM,CAAC,CAAC,OAAO;YACzB,OAAO,QAAS,KAAK,IAAI,CAAC,SAAS,GAAG,KAAK,QAAQ;QACrD,GAAG;IACL;IAEA,MAAM,iBAAiB;QACrB,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,qBAAqB,CAAC,iBAAiB;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,iBAAiB;YAEjB,MAAM,YAAY,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,IAAI,KAAK,MAAM;oBACf,MAAM,KAAK,IAAI,CAAC,IAAI;oBACpB,WAAW,KAAK,IAAI,CAAC,SAAS;oBAC9B,UAAU,KAAK,QAAQ;oBACvB,OAAO,KAAK,IAAI,CAAC,KAAK,IAAI;gBAC5B,CAAC;YAED,MAAM,aAAa;YAEnB,MAAM,eAA8C;gBAClD;gBACA;YACF;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,mIAAA,CAAA,gBAA8B,AAAD,EAAE;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,IAAI,OAAO,KAAK,KAAK,wBAAwB;oBAC3C,MAAM,eAAe,OAAO,IAAI,EAAE,WAAW;oBAC7C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ;gBACF;gBACA,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;YAEA,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAiB,AAAD;YACtB,MAAM;YAEN,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,YAAY;YAEZ,IAAI,mBAAmB;gBACrB,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAEF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,mBAAmB;YACjC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,MAAM,WAAW,MAAM;YAE1D,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;gBAC/B,MAAM,cAAc,6DAAwC,sBAAsB,EAAE,MAAM,GAAG,OAAO,EAAE,OAAO;gBAC7G,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IASA,MAAM,WAAsB;QAC1B;YAAE,MAAM;YAAqB,OAAO;QAAa;QACjD;YACE,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAK;;;;;;kCACN,8OAAC,8IAAA,CAAA,UAAa;wBAAC,WAAW,aAAa;;;;;;;;;;;;YAG3C,UAAU;gBACR;oBAAE,MAAM;oBAAmB,OAAO;gBAAa;gBAC/C;oBAAE,MAAM;oBAAqB,OAAO;gBAAc;gBAClD;oBAAE,MAAM;oBAAU,OAAO;gBAAQ;aAClC;QACH;QACA;YAAE,MAAM;YAAU,OAAO;QAAQ;WAC7B,oBACA;YACA;gBAAE,MAAM;gBAAoB,OAAO;YAAU;SAC9C,GACC,EAAE;WACF,CAAC,oBACD;YACA;gBAAE,MAAM;gBAAY,OAAO;YAAS;SACrC,GACC,EAAE;KACP;IAED,MAAM,iBAAiB;QACrB;YACE,MAAM;YACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,OAAO;QACT;QACA;YACE,SAAS;YACT,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,0MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;QACT;WACI,gBAAgB,aAAa;YAAC;gBAChC,MAAM;gBACN,oBAAM,8OAAC,gNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;gBAC3B,OAAO;YACT;SAAE,GAAG,EAAE;KACR;IAED,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAGd,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,MAAM,MACnB,KAAK,QAAQ,iBACX,8OAAC,4IAAA,CAAA,eAAY;wCAAW,MAAM;wCAAc,cAAc;;0DACxD,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,gBAAgB,CAAC,OAAS,CAAC;oDAC1C,WAAU;;wDAET,KAAK,KAAK;wDACV,6BACC,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;iFAErB,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAK7B,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,WAAU;0DAC5B,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC,4IAAA,CAAA,mBAAgB;wDAEf,OAAO;wDACP,SAAS,IAAM,gBAAgB;kEAE/B,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,KAAK,IAAI;4DACf,WAAU;;gEAET,KAAK,KAAK;gEACV,KAAK,KAAK,kBACT,8OAAC;oEAAK,WAAU;8EAA+E;;;;;;;;;;;;uDAV9F,KAAK,IAAI;;;;;;;;;;;uCAnBH;;;;6DAuCnB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,KAAK;4CACV,KAAK,KAAK,kBACT,8OAAC;gDAAK,WAAU;0DAA+E;;;;;;;uCAN5F,KAAK,IAAI;;;;;;;;;;0CAetB,8OAAC;gCAAI,WAAU;;oCACZ,mBAAmB,kCAClB;;0DACE,8OAAC,6IAAA,CAAA,UAAgB;gDACf,UAAU,kBAAkB,UAAU;;;;;;4CAEvC,KAAK,MAAM,GAAG,mBACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,YAAY;oDAC3B,WAAU;;sEAEV,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEAExB,8OAAC;4DAAK,WAAU;sEACb,KAAK,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;;;;;;;;;;;;;;;;;0DAK7D,8OAAC,mIAAA,CAAA,UAAO;;kEACN,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;sEAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD,OACF,aAAa,aAAa,aAAa,WACrC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;;;;;;kEAIZ,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,WAAU;;0EACxB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEAAC,WAAU;kFAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;4EAAC,WAAU;sFACvB,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD,OACF,aAAa,aAAa,aAAa,WACrC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;kFAGV,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FACV,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa,kBACrB,aAAa,aAAa,aAAa,WACrC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;0FAER,8OAAC;gFAAE,WAAU;0FACV,kBACG,MAAM,aAAa,sBACnB,aAAa,aAAa;;;;;;;;;;;;;;;;;;0EAKpC,8OAAC;gEAAI,WAAU;0EACZ,gCACC;;wEACG,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,kIAAA,CAAA,SAAM;gFACL,OAAO;gFACP,SAAQ;gFACR,WAAU;0FAGT,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;oFACH,MAAM,KAAK,IAAI;oFACf,WAAU;;wFAET,KAAK,IAAI;sGACV,8OAAC;sGAAM,KAAK,KAAK;;;;;;;;;;;yGAGnB,8OAAC;oFACC,SAAS,KAAK,OAAO;oFACrB,WAAU;;wFAET,KAAK,IAAI;sGACV,8OAAC;sGAAM,KAAK,KAAK;;;;;;;;;;;;+EAhBhB,KAAK,IAAI,IAAI,KAAK,KAAK;;;;;sFAqBhC,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,WAAU;4EACV,SAAS;gFACP,IAAI;oFACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CACvC,uBACA,CAAC;oFAEH,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;wFACzB,OAAO,IAAI,CAAC;wFACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;wFACjB,aAAa,UAAU,CAAC;wFACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oFAChB;gFACF,EAAE,OAAO,OAAO;oFACd,QAAQ,KAAK,CAAC,iBAAiB;oFAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gFACd;4EACF;;8FAEA,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;8FAAK;;;;;;;;;;;;;iGAIV;8EACE,cAAA,8OAAC;wEAAI,WAAU;;4EACZ;gFACC;oFACE,MAAM;oFACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;wFAAC,WAAU;;;;;;oFAC5B,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBACE,8OAAC,wNAAA,CAAA,gBAAa;wFAAC,WAAU;;;;;;oFAE3B,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFACvB,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBACE,8OAAC,oNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFAEzB,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBAAM,8OAAC,0MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFACxB,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBACE,8OAAC,oNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFAEzB,OAAO;gFACT;6EACD,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC,kIAAA,CAAA,SAAM;oFACL,OAAO;oFACP,SAAQ;oFACR,WAAU;8FAGV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wFACH,MAAM,KAAK,IAAI;wFACf,WAAU;;4FAET,KAAK,IAAI;0GACV,8OAAC;0GAAM,KAAK,KAAK;;;;;;;;;;;;mFAPd,KAAK,IAAI;;;;;0FAWlB,8OAAC,kIAAA,CAAA,SAAM;gFACL,SAAS;gFACT,WAAU;;kGAEV,8OAAC,kMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;kGAChB,8OAAC;kGAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qEAUtB;kDACE,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEAAe;;;;;;;;;;;8DAG5B,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEAAiB;;;;;;;;;;;;;;;;;;kDAMpC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAER,2BACC,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAEb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,8OAAC;;kCACC,8OAAC;wBACC,WAAW,CAAC,uGAAuG,EAAE,aAAa,kBAAkB,oBAChJ;kCAEJ,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;gDACZ,CAAC,mBAAmB,iBAAiB,mBACpC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,YAAY;4DACZ;wDACF;wDACA,WAAU;;0EAEV,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DACvB,KAAK,MAAM,GAAG,mBACb,8OAAC;gEAAK,WAAU;0EACb,KAAK,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;;;;;;;;;;;;;;;;;8DAM/D,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gCAKlB,CAAC,mBAAmB,iBAAiB,mBACpC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;0DAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD,OACF,aAAa,aAAa,aAAa,WACrC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;0DAGV,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEACV,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa,kBACrB,aAAa,aAAa,aAAa,WACrC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;kEAER,8OAAC;wDAAE,WAAU;kEACV,kBACG,MAAM,aAAa,sBACnB,aAAa,aAAa;;;;;;;;;;;;;;;;;;;;;;;8CAOxC,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,OACb,KAAK,QAAQ,iBACX,8OAAC;4CAAiC,WAAU;;8DAC1C,8OAAC;oDACC,WAAU;oDACV,SAAS;;sEAET,8OAAC;4DAAK,WAAU;sEACb,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG,KAAK,KAAK;;;;;;wDAE1D,iCACC,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;iFAErB,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;gDAG1B,kCACC,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC,4JAAA,CAAA,UAAI;4DAEH,MAAM,KAAK,IAAI;4DACf,WAAU;4DACV,SAAS;;gEAER,KAAK,KAAK;gEACV,KAAK,KAAK,kBACT,8OAAC;oEAAK,WAAU;8EAAiE;;;;;;;2DAP9E,KAAK,IAAI;;;;;;;;;;;2CAlBd,KAAK,KAAK,EAAE;;;;iEAmCtB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS;;8DAET,8OAAC;oDAAI,WAAU;8DACZ,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG,KAAK,KAAK;;;;;;gDAE1D,KAAK,KAAK,kBACT,8OAAC;oDAAK,WAAU;8DAA4D;;;;;;;2CATzE,KAAK,IAAI;;;;;;;;;;8CAkBtB,8OAAC;oCAAI,WAAU;;wCACZ,iCACC;sDACE,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS;oDACP,IAAI;wDACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CACvC,uBACA,CAAC;wDAEH,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;4DACzB,OAAO,IAAI,CAAC;4DACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;4DACjB,aAAa,UAAU,CAAC;4DACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wDAChB;oDACF,EAAE,OAAO,OAAO;wDACd,QAAQ,KAAK,CAAC,iBAAiB;wDAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oDACd;oDACA;gDACF;;kEAEA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;;wCAKX,mCACC;sDACE,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS;oDACP;oDACA;gDACF;;kEAEA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;;wCAKX,CAAC,mBAAmB,CAAC,mCACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,SAAS;kEAAY;;;;;;;;;;;8DAIjD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiB,SAAS;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU5D,mCAAqB,8OAAC,uJAAA,CAAA,UAA0B;;;;;;;;;;;0BAInD,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAU,cAAc;0BACpC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAY;wCACpB,KAAK,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;wCAAG;;;;;;;8CAEzE,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;sCACZ,KAAK,MAAM,KAAK,kBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;qDAGvC;;oCACG,KAAK,GAAG,CAAC,CAAC,qBACT,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KACE,KAAK,IAAI,CAAC,KAAK,EAAE,WAAW,UACxB,KAAK,IAAI,CAAC,KAAK,GACf,GAAG,8DAAwC,2BAA2B,KAAK,IAAI,CAAC,KAAK,EAAE,WAAW,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,iCAAiC;oDAElM,KAAK,KAAK,IAAI,CAAC,IAAI;oDACnB,OAAO;oDACP,QAAQ;oDACR,WAAU;oDACV,SAAS,CAAC;wDACR,MAAM,SAAS,EAAE,MAAM;wDACvB,OAAO,GAAG,GAAG;oDACf;;;;;;8DAEF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAoC,KAAK,IAAI,CAAC,IAAI;;;;;;sEAChE,8OAAC;4DAAE,WAAU;;8EACX,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,KAAK,IAAI,CAAC,SAAS;gEAAC;;;;;;;;;;;;;8DAGzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,mBAAmB,KAAK,MAAM,EAAE,KAAK,QAAQ,GAAG;sEAE/D,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;4DAAK,WAAU;sEAAmB,KAAK,QAAQ;;;;;;sEAChD,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,mBAAmB,KAAK,MAAM,EAAE,KAAK,QAAQ,GAAG;sEAE/D,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAGpB,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,eAAe,KAAK,MAAM;8DAC1C;;;;;;;2CA5CO,KAAK,EAAE;;;;;kDAkDnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;;sEACd,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB;wDAAoB;;;;;;;;;;;;;;;;;;;;;;;;;sCAQjC,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,YAAY;8CAAQ;;;;;;gCAG5D,KAAK,MAAM,GAAG,mBACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,8BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAAwF;;qEAIzG;;0DACE,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;4CAC5B;4CAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD;uCAEe", "debugId": null}}, {"offset": {"line": 3505, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Footer.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport {\r\n  FaFacebookF,\r\n  FaTwitter,\r\n  FaInstagram,\r\n  FaLinkedinIn,\r\n  FaTumblr,\r\n  FaPinterestP,\r\n  FaEnvelope,\r\n} from 'react-icons/fa';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-black text-gray-300 px-6 py-16\">\r\n      <div className=\"container mx-auto max-w-7xl space-y-16\">\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between gap-6\">\r\n          <Link href=\"/\" className=\"flex items-center gap-2\">\r\n            <Image\r\n              src=\"/logo_black.png\"\r\n              alt=\"Logo\"\r\n              width={200}\r\n              height={40}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n\r\n          <div className=\"flex flex-wrap justify-center gap-1\">\r\n            {[\r\n              { href: 'mailto:<EMAIL>', icon: FaEnvelope, label: 'Email Us' },\r\n              {\r\n                href: 'https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09',\r\n                icon: FaTwitter,\r\n                label: 'Twitter',\r\n              },\r\n              {\r\n                href: 'https://www.facebook.com/share/1FNYcyqawH/',\r\n                icon: FaFacebookF,\r\n                label: 'Facebook',\r\n              },\r\n              {\r\n                href: 'https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==',\r\n                icon: FaInstagram,\r\n                label: 'Instagram',\r\n              },\r\n              {\r\n                href: 'https://www.linkedin.com/company/uest-edtech/',\r\n                icon: FaLinkedinIn,\r\n                label: 'LinkedIn',\r\n              },\r\n              { href: 'https://pin.it/1Di0EFtAa', icon: FaPinterestP, label: 'Pinterest' },\r\n              {\r\n                href: 'https://www.tumblr.com/uestedtech?source=share',\r\n                icon: FaTumblr,\r\n                label: 'Tumblr',\r\n              },\r\n            ].map(({ href, icon: Icon, label }) => (\r\n              <div key={label} className=\"flex flex-col items-center\">\r\n                <Link\r\n                  href={href}\r\n                  className=\"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition\"\r\n                  title={label}\r\n                >\r\n                  <Icon className=\"text-xl text-white hover:text-gray-400 transition\" />\r\n                </Link>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10\">\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">About</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Tutors\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/support\" className=\"hover:text-white transition\">\r\n                  Support\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/careers\" className=\"hover:text-white transition\">\r\n                  Careers\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">For Students</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/student/login\" className=\"hover:text-white transition\">\r\n                  Student Login\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Online Tutor\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/uwhiz\" className=\"hover:text-white transition\">\r\n                  Uwhiz\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Contact</h3>\r\n            <address className=\"not-italic text-sm space-y-1 leading-relaxed\">\r\n              <p>Head Office</p>\r\n              <p>4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641</p>\r\n              <p>Contact: +91 96 877 877 88</p>\r\n              <p>Email: <EMAIL></p>\r\n            </address>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Apps</h3>\r\n           <Link href=\"https://play.google.com/store/apps/details?id=com.uest\" target=\"_blank\">\r\n            <Image\r\n              src=\"/playstore.png\"\r\n              alt=\"Google Play Store\"\r\n              width={180}\r\n              height={50}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom Bar */}\r\n        <div className=\"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4\">\r\n          <p>© 2025 uest.in. All rights reserved.</p>\r\n          <div className=\"flex gap-4\">\r\n            <Link href=\"/terms-and-conditions\" className=\"hover:text-white transition\">\r\n              Terms & Conditions\r\n            </Link>\r\n            <Link href=\"/privacy-policy\" className=\"hover:text-white transition\">\r\n              Privacy Policy\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAA8B,MAAM,8IAAA,CAAA,aAAU;oCAAE,OAAO;gCAAW;gCAC1E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,YAAS;oCACf,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,eAAY;oCAClB,OAAO;gCACT;gCACA;oCAAE,MAAM;oCAA4B,MAAM,8IAAA,CAAA,eAAY;oCAAE,OAAO;gCAAY;gCAC3E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,WAAQ;oCACd,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,iBAChC,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC;4CAAK,WAAU;;;;;;;;;;;mCANV;;;;;;;;;;;;;;;;8BAahB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;sDAIhE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOpE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiB,WAAU;0DAA8B;;;;;;;;;;;sDAItE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOlE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAIP,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACvD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyD,QAAO;8CAC1E,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAA8B;;;;;;8CAG3E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAkB,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;uCAEe", "debugId": null}}, {"offset": {"line": 3916, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/LeaderboardUserApi.ts"], "sourcesContent": ["import { axiosInstance } from \"@/lib/axios\";\r\n\r\nexport const getMockExamLeaderboard = async (\r\n  timeframe: string,\r\n  page: number = 1,\r\n  limit: number = 10\r\n): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(\r\n      `/mock-exam-leaderboard/leaderboard/${timeframe}?page=${page}&limit=${limit}`,\r\n      {\r\n        headers: {\r\n          \"Server-Select\": \"uwhizServer\",\r\n        },\r\n      }\r\n    );\r\n    return { success: true, data: response.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam leaderboard data: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMockExamTopThreeStudents = async (\r\n): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(\r\n      `/mock-exam-leaderboard/previous-day`,\r\n      {\r\n        headers: {\r\n          \"Server-Select\": \"uwhizServer\",\r\n        },\r\n      }\r\n    );\r\n    return { success: true, data: response.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get yesterday's top performers: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const storeReaction = async (\r\n  studentId: string,\r\n  reactionType: string,\r\n  reactorId: string | null\r\n): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.post(\r\n      `/reactions`,\r\n      {\r\n        studentId,\r\n        reactionType,\r\n        reactorId\r\n      },\r\n      {\r\n        headers: {\r\n          \"Server-Select\": \"uwhizServer\",\r\n        },\r\n      }\r\n    );\r\n    return { success: true, data: response.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to send reaction: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;;AAAA;;AAEO,MAAM,yBAAyB,OACpC,WACA,OAAe,CAAC,EAChB,QAAgB,EAAE;IAElB,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CACtC,CAAC,mCAAmC,EAAE,UAAU,MAAM,EAAE,KAAK,OAAO,EAAE,OAAO,EAC7E;YACE,SAAS;gBACP,iBAAiB;YACnB;QACF;QAEF,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,0CAA0C,EAChD,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,8BAA8B;IAEzC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CACtC,CAAC,mCAAmC,CAAC,EACrC;YACE,SAAS;gBACP,iBAAiB;YACnB;QACF;QAEF,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,0CAA0C,EAChD,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,gBAAgB,OAC3B,WACA,cACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CACvC,CAAC,UAAU,CAAC,EACZ;YACE;YACA;YACA;QACF,GACA;YACE,SAAS;gBACP,iBAAiB;YACnB;QACF;QAEF,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,yBAAyB,EAC/B,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 3987, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/mock-exam-resultApi.ts"], "sourcesContent": ["import {axiosInstance} from \"../lib/axios\";\r\n\r\nexport const saveMockExamResult = async (data: any): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.post(\"/mock-exam-result\", data, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam result: ${\r\n        error.response?.data?.message || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMockExamResults = async (\r\n  studentId: string,\r\n  page: number = 1,\r\n  limit: number = 10,\r\n  filter: { isWeekly?: boolean } = {} \r\n): Promise<any> => {\r\n  try {\r\n    const query = new URLSearchParams({\r\n      page: page.toString(),\r\n      limit: limit.toString(),\r\n      ...(filter.isWeekly !== undefined && { isWeekly: filter.isWeekly.toString() }),\r\n    }).toString();\r\n    const response = await axiosInstance.get(`/mock-exam-result/${studentId}?${query}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam result: ${\r\n        error.response?.data?.message || error.message\r\n      }`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,qBAAqB,MAAM;YACnE,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QACJ;IACF;AACF;AAEO,MAAM,qBAAqB,OAChC,WACA,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAAiC,CAAC,CAAC;IAEnC,IAAI;QACF,MAAM,QAAQ,IAAI,gBAAgB;YAChC,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;YACrB,GAAI,OAAO,QAAQ,KAAK,aAAa;gBAAE,UAAU,OAAO,QAAQ,CAAC,QAAQ;YAAG,CAAC;QAC/E,GAAG,QAAQ;QACX,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU,CAAC,EAAE,OAAO,EAAE;YAClF,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 4042, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/mock-test/mockExamButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { getMockExamResults } from \"@/services/mock-exam-resultApi\";\r\n\r\nexport default function MockExamButton() {\r\n  const router = useRouter();\r\n  const [isButtonDisabled, setIsButtonDisabled] = useState(false);\r\n  const [remainingSeconds, setRemainingSeconds] = useState<number | null>(null);\r\n  const [studentId, setStudentId] = useState<string | null>(null);\r\n  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);\r\n  const reEnableTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  useEffect(() => {\r\n    try {\r\n      const data = localStorage.getItem(\"student_data\");\r\n      const fetchedStudentId = data ? JSON.parse(data).id : null;\r\n      setStudentId(fetchedStudentId);\r\n    } catch (error) {\r\n      console.error(\"Error retrieving studentId:\", error);\r\n      setStudentId(null);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const checkExamAttempt = async () => {\r\n      if (!studentId) {\r\n        setIsButtonDisabled(true);\r\n        setRemainingSeconds(null);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const response = await getMockExamResults(studentId, 1, 1, {\r\n          isWeekly: false,\r\n        });\r\n        if (response.success && response.data.data.mockExamResults.length > 0) {\r\n          const latestExam = response.data.data.mockExamResults[0];\r\n          const examDate = new Date(latestExam.createdAt)\r\n            .toISOString()\r\n            .split(\"T\")[0];\r\n          const today = new Date().toISOString().split(\"T\")[0];\r\n\r\n          if (examDate === today) {\r\n            setIsButtonDisabled(true);\r\n\r\n            const now = new Date();\r\n            const nextMidnight = new Date();\r\n            nextMidnight.setDate(now.getDate() + 1);\r\n            nextMidnight.setHours(0, 0, 0, 0);\r\n            const remainingMs = nextMidnight.getTime() - now.getTime();\r\n            const remainingSec = Math.ceil(remainingMs / 1000);\r\n            setRemainingSeconds(remainingSec > 0 ? remainingSec : null);\r\n\r\n            countdownIntervalRef.current = setInterval(() => {\r\n              setRemainingSeconds((prev) => {\r\n                if (prev === null || prev <= 1) {\r\n                  clearInterval(countdownIntervalRef.current!);\r\n                  return null;\r\n                }\r\n                return prev - 1;\r\n              });\r\n            }, 1000);\r\n\r\n            reEnableTimeoutRef.current = setTimeout(() => {\r\n              setIsButtonDisabled(false);\r\n              setRemainingSeconds(null);\r\n            }, remainingMs);\r\n          } else {\r\n            setIsButtonDisabled(false);\r\n            setRemainingSeconds(null);\r\n          }\r\n        } else {\r\n          setIsButtonDisabled(false);\r\n          setRemainingSeconds(null);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error checking exam attempt:\", error);\r\n        toast.error(\"Failed to verify exam eligibility.\");\r\n        setIsButtonDisabled(true);\r\n        setRemainingSeconds(null);\r\n      }\r\n    };\r\n\r\n    checkExamAttempt();\r\n\r\n    return () => {\r\n      if (countdownIntervalRef.current) {\r\n        clearInterval(countdownIntervalRef.current);\r\n      }\r\n      if (reEnableTimeoutRef.current) {\r\n        clearTimeout(reEnableTimeoutRef.current);\r\n      }\r\n    };\r\n  }, [studentId]);\r\n\r\n  const handleMockExam = () => {\r\n    if (!studentId) {\r\n      toast.error(\"Please log in to attempt the exam.\");\r\n      router.push(\"/login\");\r\n      return;\r\n    }\r\n\r\n    if (isButtonDisabled && remainingSeconds) {\r\n      toast.error(\r\n        `You can attempt the exam again in ${formatRemainingTime()}.`\r\n      );\r\n      return;\r\n    }\r\n    router.push(\"/mock-test\");\r\n  };\r\n\r\n  const formatRemainingTime = () => {\r\n    if (remainingSeconds === null) return null;\r\n    const hours = Math.floor(remainingSeconds / 3600);\r\n    const minutes = Math.floor((remainingSeconds % 3600) / 60);\r\n    const seconds = remainingSeconds % 60;\r\n    return `${hours.toString().padStart(2, \"0\")}:${minutes\r\n      .toString()\r\n      .padStart(2, \"0\")}:${seconds.toString().padStart(2, \"0\")}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center\">\r\n      <Button\r\n        className=\"w-full max-w-xs bg-black text-white font-bold text-lg p-9 py-4\r\n               border-2 border-[#FD904B] hover:border-[#E88143]\r\n               transition-all duration-300 hover:scale-105\"\r\n        onClick={handleMockExam}\r\n        disabled={isButtonDisabled || !studentId}\r\n      >\r\n        🚀 Try Daily Quiz\r\n      </Button>\r\n\r\n      {isButtonDisabled && remainingSeconds && (\r\n        <p className=\"text-center mt-3 text-sm text-gray-400\">\r\n          ⏳ Next attempt:{\" \"}\r\n          <span className=\"font-semibold\">{formatRemainingTime()}</span>\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAC3D,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,MAAM,mBAAmB,OAAO,KAAK,KAAK,CAAC,MAAM,EAAE,GAAG;YACtD,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,aAAa;QACf;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,CAAC,WAAW;gBACd,oBAAoB;gBACpB,oBAAoB;gBACpB;YACF;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,4IAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,GAAG,GAAG;oBACzD,UAAU;gBACZ;gBACA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,GAAG;oBACrE,MAAM,aAAa,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;oBACxD,MAAM,WAAW,IAAI,KAAK,WAAW,SAAS,EAC3C,WAAW,GACX,KAAK,CAAC,IAAI,CAAC,EAAE;oBAChB,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAEpD,IAAI,aAAa,OAAO;wBACtB,oBAAoB;wBAEpB,MAAM,MAAM,IAAI;wBAChB,MAAM,eAAe,IAAI;wBACzB,aAAa,OAAO,CAAC,IAAI,OAAO,KAAK;wBACrC,aAAa,QAAQ,CAAC,GAAG,GAAG,GAAG;wBAC/B,MAAM,cAAc,aAAa,OAAO,KAAK,IAAI,OAAO;wBACxD,MAAM,eAAe,KAAK,IAAI,CAAC,cAAc;wBAC7C,oBAAoB,eAAe,IAAI,eAAe;wBAEtD,qBAAqB,OAAO,GAAG,YAAY;4BACzC,oBAAoB,CAAC;gCACnB,IAAI,SAAS,QAAQ,QAAQ,GAAG;oCAC9B,cAAc,qBAAqB,OAAO;oCAC1C,OAAO;gCACT;gCACA,OAAO,OAAO;4BAChB;wBACF,GAAG;wBAEH,mBAAmB,OAAO,GAAG,WAAW;4BACtC,oBAAoB;4BACpB,oBAAoB;wBACtB,GAAG;oBACL,OAAO;wBACL,oBAAoB;wBACpB,oBAAoB;oBACtB;gBACF,OAAO;oBACL,oBAAoB;oBACpB,oBAAoB;gBACtB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,oBAAoB;gBACpB,oBAAoB;YACtB;QACF;QAEA;QAEA,OAAO;YACL,IAAI,qBAAqB,OAAO,EAAE;gBAChC,cAAc,qBAAqB,OAAO;YAC5C;YACA,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,aAAa,mBAAmB,OAAO;YACzC;QACF;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,iBAAiB;QACrB,IAAI,CAAC,WAAW;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,oBAAoB,kBAAkB;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,kCAAkC,EAAE,sBAAsB,CAAC,CAAC;YAE/D;QACF;QACA,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,sBAAsB;QAC1B,IAAI,qBAAqB,MAAM,OAAO;QACtC,MAAM,QAAQ,KAAK,KAAK,CAAC,mBAAmB;QAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,mBAAmB,OAAQ;QACvD,MAAM,UAAU,mBAAmB;QACnC,OAAO,GAAG,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAC5C,QAAQ,GACR,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC9D;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,kIAAA,CAAA,SAAM;gBACL,WAAU;gBAGV,SAAS;gBACT,UAAU,oBAAoB,CAAC;0BAChC;;;;;;YAIA,oBAAoB,kCACnB,8OAAC;gBAAE,WAAU;;oBAAyC;oBACpC;kCAChB,8OAAC;wBAAK,WAAU;kCAAiB;;;;;;;;;;;;;;;;;;AAK3C", "debugId": null}}, {"offset": {"line": 4203, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 4228, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/SpinningWheel.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { toast } from \"sonner\";\r\nimport Image from \"next/image\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\n\r\ninterface Prize {\r\n  id: string;\r\n  name: string;\r\n  image: string;\r\n  type: \"item\" | \"coins\" | \"nothing\";\r\n}\r\ninterface SpinningWheelProps {\r\n  isOpen?: boolean;\r\n  setIsOpen?: React.Dispatch<React.SetStateAction<boolean>>;\r\n}\r\nconst SpinningWheel = ({ isOpen: externalIsOpen, setIsOpen: externalSetIsOpen }: SpinningWheelProps) => {\r\n  const [isOpenInternal, setIsOpenInternal] = useState(false);\r\n  const isOpen = externalIsOpen !== undefined ? externalIsOpen : isOpenInternal;\r\n  const setIsOpen = externalSetIsOpen || setIsOpenInternal;\r\n  const [isSpinning, setIsSpinning] = useState(false);\r\n  const [prize, setPrize] = useState<Prize | null>(null);\r\n  const [rotation, setRotation] = useState(0);\r\n  const [hasSpun, setHasSpun] = useState(false);\r\n  const [user, setUser] = useState<{ id: string; type: \"STUDENT\" | \"CLASS\" } | null>(null);\r\n  const [isEligible, setIsEligible] = useState(false);\r\n  const [windowHeight, setWindowHeight] = useState(500);\r\n  const [windowWidth, setWindowWidth] = useState(500);\r\n  const [imageError, setImageError] = useState(false);\r\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\r\n  const isMobile = useIsMobile();\r\n  const spinSoundRef = useRef<HTMLAudioElement | null>(null);\r\n\r\n\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    spinSoundRef.current = new Audio(\"/spinsound.mp3\");\r\n  }, []);\r\n  const prizes: Prize[] = [\r\n    { id: \"2ee90e37-b176-45ba-991f-7e18d3d0191b\", name: \"Laptop\", image: \"/laptop.svg\", type: \"item\" },\r\n    { id: \"fbec0133-1fff-45ae-b50e-4d5ad2988861\", name: \"Keychain\", image: \"/keychain.svg\", type: \"item\" },\r\n    { id: \"7d52f598-5a08-41d3-8e8d-c1fd0c821e29\", name: \"3 coins\", image: \"/3coins.svg\", type: \"coins\" },\r\n    { id: \"986b9573-9875-4ef8-9f6e-ec5e5f92d014\", name: \"uest super card\", image: \"/uestcard.svg\", type: \"item\" },\r\n    { id: \"e5328123-d443-4285-b600-9b44d55b7b06\", name: \"Better luck next time\", image: \"/betterluck.svg\", type: \"nothing\" },\r\n    { id: \"6507cf36-a690-4d1c-bd21-5f4f823b050a\", name: \"1 notebook\", image: \"/1notebook.svg\", type: \"item\" },\r\n  ];\r\n\r\n  const prizeWeights = [\r\n    { name: \"Better luck next time\", weight: 25 },\r\n    { name: \"3 coins\", weight: 40 },\r\n    { name: \"1 notebook\", weight: 20 },\r\n    { name: \"Keychain\", weight: 10 },\r\n    { name: \"uest super card\", weight: 5 },\r\n    { name: \"Laptop\", weight: 0 },\r\n  ];\r\n\r\n  const selectPrizeByProbability = (): Prize => {\r\n    const totalWeight = prizeWeights.reduce((sum, item) => sum + item.weight, 0);\r\n    const random = Math.random() * totalWeight;\r\n\r\n    let currentWeight = 0;\r\n    for (const weightItem of prizeWeights) {\r\n      currentWeight += weightItem.weight;\r\n      if (random <= currentWeight) {\r\n        return prizes.find(prize => prize.name === weightItem.name) || prizes[0];\r\n      }\r\n    }\r\n\r\n    return prizes.find(prize => prize.name === \"Better luck next time\") || prizes[0];\r\n  };\r\n\r\n  const encouragingMessages: { [key: string]: string } = {\r\n    \"Laptop\": \"Whoa! You just hit the jackpot and won a shiny new laptop! 🧑‍💻✨\",\r\n    \"Keychain\": \"Yay! A cool keychain to show off your spinning skills! 🔑🎉\",\r\n    \"3 coins\": \"Yayy! You've bagged 3 golden coins! 💰💰💰\",\r\n    \"uest super card\": \"Boom! You've unlocked the UEST Super Card !!! you're a legend! 💳🌟\",\r\n    \"1 notebook\": \"Nice! A fresh notebook to doodle, dream, and jot your genius ideas! 📓🖍️\",\r\n    \"Better luck next time\": \"Oh no! No prize this time... but don’t give up, superstar! 🌧️💪\",\r\n  };\r\n\r\n  useEffect(() => {\r\n    const studentRaw = localStorage.getItem(\"student_data\");\r\n    const classRaw = localStorage.getItem(\"user\");\r\n    const student = studentRaw ? JSON.parse(studentRaw) : null;\r\n    const classUser = classRaw ? JSON.parse(classRaw) : null;\r\n\r\n    if (student?.id) setUser({ id: student.id, type: \"STUDENT\" });\r\n    else if (classUser?.id) setUser({ id: classUser.id, type: \"CLASS\" });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const updateWindowDimensions = () => {\r\n      setWindowHeight(window.innerHeight);\r\n      setWindowWidth(window.innerWidth);\r\n    };\r\n\r\n    updateWindowDimensions();\r\n    window.addEventListener('resize', updateWindowDimensions);\r\n\r\n    return () => window.removeEventListener('resize', updateWindowDimensions);\r\n  }, []);\r\n\r\n  const checkSpinEligibility = async () => {\r\n    if (!user) return;\r\n    try {\r\n      const res = await axiosInstance.get(\"/spin/can-spin\", {\r\n        params: {\r\n          modelId: user.id,\r\n          modelType: user.type,\r\n        },\r\n      });\r\n      setIsEligible(res.data.success && res.data.eligible === true);\r\n    } catch (err) {\r\n      console.error(\"Error checking spin eligibility:\", err);\r\n      setIsEligible(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    checkSpinEligibility();\r\n  }, [user]);\r\n\r\n  // const rewardMap: { [key: string]: string } = {\r\n  //   \"Laptop\": \"2ee90e37-b176-45ba-991f-7e18d3d0191b\",\r\n  //   \"Keychain\": \"fbec0133-1fff-45ae-b50e-4d5ad2988861\",\r\n  //   \"3 coins\": \"7d52f598-5a08-41d3-8e8d-c1fd0c821e29\",\r\n  //   \"uest super card\": \"986b9573-9875-4ef8-9f6e-ec5e5f92d014\",\r\n  //   \"Better luck next time\": \"e5328123-d443-4285-b600-9b44d55b7b06\",\r\n  //   \"1 notebook\": \"6507cf36-a690-4d1c-bd21-5f4f823b050a\",\r\n  // };\r\n\r\n  const handleClose = () => {\r\n    setIsOpen(false);\r\n    setHasSpun(false);\r\n    setPrize(null);\r\n    setRotation(0);\r\n    checkSpinEligibility();\r\n  };\r\n\r\n  const spinWheel = () => {\r\n    if (!user) {\r\n      toast.error(\"Please login to spin the wheel\");\r\n      return;\r\n    }\r\n\r\n    if (!isSpinning) {\r\n      setPrize(null);\r\n      setIsSpinning(true);\r\n      setRotation(0);\r\n\r\n      spinSoundRef.current?.play().catch(() => { });\r\n\r\n      const spinInterval = setInterval(() => {\r\n        setRotation((prev) => prev + 30);\r\n      }, 50);\r\n\r\n      const selectedPrize = selectPrizeByProbability();\r\n\r\n      setTimeout(async () => {\r\n        clearInterval(spinInterval);\r\n        setIsSpinning(false);\r\n        setPrize(selectedPrize);\r\n        setHasSpun(true);\r\n\r\n        try {\r\n          const rewardId = selectedPrize.id;\r\n\r\n          if (rewardId) {\r\n            await axiosInstance.post(\"/spin/save-reward\", {\r\n              modelId: user.id,\r\n              modelType: user.type,\r\n              rewardId,\r\n            });\r\n          }\r\n\r\n          if (selectedPrize.name === \"3 coins\") {\r\n            await axiosInstance.post(\"/spin/credit-coins\", {\r\n              modelId: user.id,\r\n              modelType: user.type,\r\n              coins: 3,\r\n            });\r\n          }\r\n\r\n          if (\r\n            selectedPrize.name === \"Keychain\" ||\r\n            selectedPrize.name === \"1 notebook\" ||\r\n            selectedPrize.name === \"uest super card\"\r\n          ) {\r\n            try {\r\n              const orderResponse = await axiosInstance.post(\"/store/create-spin-order\", {\r\n                itemId: selectedPrize.id,\r\n                itemName: selectedPrize.name,\r\n              });\r\n\r\n              if (orderResponse.data.success) {\r\n                toast.success(`🎉 Order created for ${selectedPrize.name}! Check your orders in profile.`);\r\n              } else {\r\n                toast.error(\r\n                  `Failed to create order for ${selectedPrize.name}: ${orderResponse.data.message || \"Unknown error\"}`\r\n                );\r\n              }\r\n            } catch (orderErr: any) {\r\n              toast.error(\r\n                `Failed to create order for ${selectedPrize.name}: ${orderErr.response?.data?.message || orderErr.message}`\r\n              );\r\n            }\r\n          }\r\n        } catch (err: any) {\r\n          console.error(\"Failed to log spin reward or credit coins\", err);\r\n        }\r\n\r\n        checkSpinEligibility();\r\n      }, 4000);\r\n    }\r\n  };\r\n\r\n\r\n  const payToSpinAgain = async () => {\r\n    try {\r\n      if (!user) {\r\n        toast.error(\"Please login to spin the wheel\");\r\n        return;\r\n      }\r\n\r\n      const res = await axiosInstance.post(\"/spin/deduct-spin-coins\", {\r\n        modelId: user.id,\r\n        modelType: user.type,\r\n      });\r\n\r\n      if (res?.data?.success) {\r\n        toast.success(\"10 coins deducted! You can spin again.\");\r\n        spinWheel();\r\n      } else {\r\n        toast.error(res?.data?.message || \"Failed to deduct coins\");\r\n      }\r\n    } catch (err: any) {\r\n      const fallbackMessage = err?.response?.data?.message || err?.message || \"Unknown error\";\r\n      toast.error(fallbackMessage);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <motion.button\r\n        whileHover={{ scale: 1.05, boxShadow: \"0 10px 25px rgba(0,0,0,0.2)\" }}\r\n        whileTap={{ scale: 0.95 }}\r\n        onClick={() => setIsOpen(true)}\r\n        className=\"px-4 py-2 sm:px-6 sm:py-3 bg-customOrange cursor-pointer text-primary-foreground font-semibold rounded-lg shadow-md transition-colors text-sm sm:text-base\"\r\n      >\r\n        🎡 Spin the Wheel!\r\n      </motion.button>\r\n\r\n      <AnimatePresence>\r\n        {isOpen && (\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-1 xs:p-2 sm:p-4 overflow-hidden\"\r\n            onClick={handleClose}\r\n          >\r\n            <motion.div\r\n              initial={{ scale: 0.9, y: 20 }}\r\n              animate={{ scale: 1, y: 0 }}\r\n              exit={{ scale: 0.9, y: 20 }}\r\n              className={`bg-card/95 backdrop-blur-md rounded-2xl shadow-2xl w-full relative mx-auto border border-border/50 ring-1 ring-primary/20 ${isMobile\r\n                ? \"max-w-[95%] p-3 max-h-[95vh] min-h-[480px]\"\r\n                : \"max-w-[98%] md:max-w-[720px] lg:max-w-[900px] p-4 sm:p-6 max-h-[90vh] min-h-[500px]\"\r\n                }`}\r\n              onClick={(e) => e.stopPropagation()}\r\n            >\r\n              <button\r\n                onClick={handleClose}\r\n                className=\"absolute top-2 right-2 sm:top-4 sm:right-4 text-muted-foreground hover:text-foreground transition-colors\"\r\n                aria-label=\"Close\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  className=\"h-5 w-5 sm:h-6 sm:w-6\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  stroke=\"currentColor\"\r\n                >\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                </svg>\r\n              </button>\r\n\r\n              <div className=\"flex flex-col h-full\">\r\n                <div className=\"text-center mb-4 sm:mb-6\">\r\n                  <motion.h2\r\n                    initial={{ opacity: 0, y: -20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    className=\"text-xl sm:text-2xl md:text-3xl font-bold text-orange-400 mb-2\"\r\n                  >\r\n                    Spin & Win! 🎁\r\n                  </motion.h2>\r\n                  <p className=\"text-black\">\r\n                    <span className=\"text-red-600\">Disclaimer:</span> The spinner is for engagement and learning. No real money is involved. Coins have no cash value and cannot be exchanged for real currency.\r\n                  </p>\r\n                </div>\r\n\r\n                <div className=\"flex flex-col lg:flex-row flex-1 gap-4 sm:gap-6\">\r\n                  <AnimatePresence>\r\n                    {prize && (\r\n                      <motion.div\r\n                        initial={{ opacity: 0, y: 20, scale: 0.8 }}\r\n                        animate={{ opacity: 1, y: 0, scale: 1 }}\r\n                        exit={{ opacity: 0, y: 20, scale: 0.8 }}\r\n                        transition={{ duration: 0.5, type: \"spring\", bounce: 0.3 }}\r\n                        className=\"w-full lg:flex-1 flex flex-col justify-center items-center p-4 sm:p-6 bg-gradient-to-br from-accent/30 to-accent/60 rounded-xl border border-border shadow-lg backdrop-blur-sm\"\r\n                      >\r\n                        <motion.div\r\n                          initial={{ scale: 0.5, opacity: 0 }}\r\n                          animate={{ scale: 1, opacity: 1 }}\r\n                          transition={{ delay: 0.2, type: \"spring\", bounce: 0.4 }}\r\n                          className=\"text-center\"\r\n                        >\r\n                          <motion.div\r\n                            className=\"relative w-[78px] h-[78px] sm:w-[98px] sm:h-[98px] md:w-[125px] md:h-[125px] mx-auto mb-3 sm:mb-4\"\r\n                            whileHover={{ scale: 1.1, rotate: 5 }}\r\n                            transition={{ type: \"spring\", stiffness: 300 }}\r\n                          >\r\n                            <Image\r\n                              src={prize.image}\r\n                              alt={prize.name}\r\n                              fill\r\n                              className=\"object-contain drop-shadow-lg\"\r\n                              draggable=\"false\"\r\n                              priority\r\n                              onError={() => {\r\n                                console.error(`Failed to load image: ${prize.image}`);\r\n                              }}\r\n                            />\r\n                          </motion.div>\r\n                          <motion.h3\r\n                            initial={{ opacity: 0, y: 20 }}\r\n                            animate={{ opacity: 1, y: 0 }}\r\n                            transition={{ delay: 0.4 }}\r\n                            className=\"text-lg sm:text-xl md:text-2xl font-bold text-accent-foreground mb-1 sm:mb-2\"\r\n                          >\r\n                            {prize.name === \"Better luck next time\" ? \"Oops!\" : \"You Won \"} {prize.name}\r\n                          </motion.h3>\r\n                          <motion.p\r\n                            initial={{ opacity: 0, y: 20 }}\r\n                            animate={{ opacity: 1, y: 0 }}\r\n                            transition={{ delay: 0.6 }}\r\n                            className=\"text-sm sm:text-base md:text-lg text-muted-foreground mb-4 sm:mb-6\"\r\n                          >\r\n                            {encouragingMessages[prize.name]}\r\n                          </motion.p>\r\n                        </motion.div>\r\n                      </motion.div>\r\n                    )}\r\n                  </AnimatePresence>\r\n\r\n                  <motion.div className=\"w-full lg:flex-1 flex flex-col items-center justify-center\">\r\n                    <div className=\"relative w-full flex items-center justify-center\">\r\n                      <motion.div\r\n                        animate={{ rotate: rotation }}\r\n                        transition={{ type: \"tween\", ease: \"linear\", duration: 0.05 }}\r\n                        className=\"w-full max-w-[320px] sm:max-w-[380px] md:max-w-[420px] lg:max-w-[460px] aspect-square rounded-full flex items-center justify-center bg-background overflow-hidden mx-auto border-4 border-primary relative shadow-xl ring-2 ring-primary/20\"\r\n                        style={{\r\n                          minWidth: isMobile ? '280px' : '320px',\r\n                          minHeight: isMobile ? '280px' : '320px'\r\n                        }}\r\n                      >\r\n                        {!imageError ? (\r\n                          <div className=\"relative w-full h-full\">\r\n                            <Image\r\n                              src=\"/spin3.svg\"\r\n                              alt={prize ? prize.name : \"Spinning Wheel\"}\r\n                              fill\r\n                              className=\"object-contain\"\r\n                              draggable=\"false\"\r\n                              onError={() => {\r\n                                setImageError(true);\r\n                              }}\r\n                              priority\r\n                            />\r\n                          </div>\r\n                        ) : (\r\n                          <div className=\"w-full h-full relative\">\r\n                            <div className=\"w-full h-full rounded-full border-8 border-primary bg-gradient-to-br from-primary via-primary/80 to-accent flex items-center justify-center\">\r\n                              <div className=\"w-3/4 h-3/4 rounded-full bg-background border-2 border-border flex items-center justify-center text-primary font-bold text-lg shadow-inner\">\r\n                                SPIN WHEEL\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                        <motion.button\r\n                          onClick={!isSpinning ? (isEligible ? spinWheel : () => setShowConfirmDialog(true)) : undefined}\r\n                          whileHover={!isSpinning ? { scale: 1.05 } : {}}\r\n                          whileTap={!isSpinning ? { scale: 0.98 } : {}}\r\n                          animate={{\r\n                            rotate: isSpinning ? -rotation : 0,\r\n                            scale: isSpinning ? 1 : 1\r\n                          }}\r\n                          className={`absolute z-10 rounded-full bg-black shadow-xl shadow-primary/40 flex items-center justify-center border-4 border-background ring-2 ring-primary/30 cursor-pointer hover:from-primary/90 hover:to-primary/70 hover:shadow-2xl hover:shadow-primary/50 transition-all duration-300 ${isMobile ? 'w-[70px] h-[70px]' : 'w-[90px] h-[90px] sm:w-[100px] sm:h-[100px]'\r\n                            }`}\r\n                          disabled={isSpinning}\r\n                        >\r\n                          <span className={`text-primary-foreground font-bold text-center leading-tight px-1 ${isMobile ? 'text-[9px]' : 'text-[10px] sm:text-xs'\r\n                            }`}>\r\n                            {isSpinning ? (\r\n                              <motion.span\r\n                                animate={{ rotate: 360 }}\r\n                                transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\r\n                                className=\"inline-block\"\r\n                              >\r\n                                ⟳\r\n                              </motion.span>\r\n                            ) : isEligible ? \"SPIN\\nFREE\" : \"SPIN\\n10 COINS\"}\r\n                          </span>\r\n                        </motion.button>\r\n                      </motion.div>\r\n                    </div>\r\n\r\n                    {hasSpun && (\r\n                      <div className=\"flex justify-center mt-4 sm:mt-6\">\r\n                        <motion.button\r\n                          onClick={handleClose}\r\n                          className=\"px-8 py-3 rounded-full bg-secondary hover:bg-secondary/90 text-secondary-foreground font-semibold shadow-lg border border-border hover:shadow-xl transition-all duration-200 text-sm sm:text-base\"\r\n                          whileHover={{ scale: 1.05 }}\r\n                          whileTap={{ scale: 0.95 }}\r\n                        >\r\n                          CLOSE\r\n                        </motion.button>\r\n                      </div>\r\n                    )}\r\n                  </motion.div>\r\n                </div>\r\n              </div>\r\n\r\n              {prize && prize.type === \"nothing\" && (\r\n                <div className=\"absolute inset-0 pointer-events-none flex flex-wrap justify-center overflow-hidden\">\r\n                  {Array.from({ length: 20 }).map((_, i) => (\r\n                    <motion.div\r\n                      key={i}\r\n                      initial={{ y: -100, opacity: 0 }}\r\n                      animate={{\r\n                        y: windowHeight,\r\n                        opacity: [0, 1, 0],\r\n                        x: Math.random() * windowWidth - windowWidth / 2,\r\n                      }}\r\n                      transition={{ duration: 3, delay: i * 0.1, repeat: Infinity }}\r\n                      className=\"w-8 h-8 sm:w-10 sm:h-10 absolute\"\r\n                      style={{ left: `${Math.random() * 100}%` }}\r\n                    >\r\n                      <Image\r\n                        src=\"/sadfaceemoji.png\"\r\n                        alt=\"Sad emoji\"\r\n                        width={40}\r\n                        height={40}\r\n                        className=\"w-full h-full object-contain\"\r\n                        draggable=\"false\"\r\n                      />\r\n                    </motion.div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </motion.div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n\r\n\r\n      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Spend 10 coins to spin again?</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              This will deduct <strong>10 coins</strong> from your account. Are you sure you want to continue?\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={() => {\r\n                setShowConfirmDialog(false);\r\n                payToSpinAgain();\r\n              }}\r\n            >\r\n              Yes, spin again\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n    </>\r\n  );\r\n};\r\n\r\nexport default SpinningWheel;"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AA6BA,MAAM,gBAAgB,CAAC,EAAE,QAAQ,cAAc,EAAE,WAAW,iBAAiB,EAAsB;IACjG,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,SAAS,mBAAmB,YAAY,iBAAiB;IAC/D,MAAM,YAAY,qBAAqB;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoD;IACnF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IAMrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,GAAG,IAAI,MAAM;IACnC,GAAG,EAAE;IACL,MAAM,SAAkB;QACtB;YAAE,IAAI;YAAwC,MAAM;YAAU,OAAO;YAAe,MAAM;QAAO;QACjG;YAAE,IAAI;YAAwC,MAAM;YAAY,OAAO;YAAiB,MAAM;QAAO;QACrG;YAAE,IAAI;YAAwC,MAAM;YAAW,OAAO;YAAe,MAAM;QAAQ;QACnG;YAAE,IAAI;YAAwC,MAAM;YAAmB,OAAO;YAAiB,MAAM;QAAO;QAC5G;YAAE,IAAI;YAAwC,MAAM;YAAyB,OAAO;YAAmB,MAAM;QAAU;QACvH;YAAE,IAAI;YAAwC,MAAM;YAAc,OAAO;YAAkB,MAAM;QAAO;KACzG;IAED,MAAM,eAAe;QACnB;YAAE,MAAM;YAAyB,QAAQ;QAAG;QAC5C;YAAE,MAAM;YAAW,QAAQ;QAAG;QAC9B;YAAE,MAAM;YAAc,QAAQ;QAAG;QACjC;YAAE,MAAM;YAAY,QAAQ;QAAG;QAC/B;YAAE,MAAM;YAAmB,QAAQ;QAAE;QACrC;YAAE,MAAM;YAAU,QAAQ;QAAE;KAC7B;IAED,MAAM,2BAA2B;QAC/B,MAAM,cAAc,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE;QAC1E,MAAM,SAAS,KAAK,MAAM,KAAK;QAE/B,IAAI,gBAAgB;QACpB,KAAK,MAAM,cAAc,aAAc;YACrC,iBAAiB,WAAW,MAAM;YAClC,IAAI,UAAU,eAAe;gBAC3B,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,WAAW,IAAI,KAAK,MAAM,CAAC,EAAE;YAC1E;QACF;QAEA,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,4BAA4B,MAAM,CAAC,EAAE;IAClF;IAEA,MAAM,sBAAiD;QACrD,UAAU;QACV,YAAY;QACZ,WAAW;QACX,mBAAmB;QACnB,cAAc;QACd,yBAAyB;IAC3B;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,MAAM,UAAU,aAAa,KAAK,KAAK,CAAC,cAAc;QACtD,MAAM,YAAY,WAAW,KAAK,KAAK,CAAC,YAAY;QAEpD,IAAI,SAAS,IAAI,QAAQ;YAAE,IAAI,QAAQ,EAAE;YAAE,MAAM;QAAU;aACtD,IAAI,WAAW,IAAI,QAAQ;YAAE,IAAI,UAAU,EAAE;YAAE,MAAM;QAAQ;IACpE,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,gBAAgB,OAAO,WAAW;YAClC,eAAe,OAAO,UAAU;QAClC;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM;QACX,IAAI;YACF,MAAM,MAAM,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,kBAAkB;gBACpD,QAAQ;oBACN,SAAS,KAAK,EAAE;oBAChB,WAAW,KAAK,IAAI;gBACtB;YACF;YACA,cAAc,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK;QAC1D,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,cAAc;QAChB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAK;IAET,iDAAiD;IACjD,sDAAsD;IACtD,wDAAwD;IACxD,uDAAuD;IACvD,+DAA+D;IAC/D,qEAAqE;IACrE,0DAA0D;IAC1D,KAAK;IAEL,MAAM,cAAc;QAClB,UAAU;QACV,WAAW;QACX,SAAS;QACT,YAAY;QACZ;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,CAAC,MAAM;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,YAAY;YACf,SAAS;YACT,cAAc;YACd,YAAY;YAEZ,aAAa,OAAO,EAAE,OAAO,MAAM,KAAQ;YAE3C,MAAM,eAAe,YAAY;gBAC/B,YAAY,CAAC,OAAS,OAAO;YAC/B,GAAG;YAEH,MAAM,gBAAgB;YAEtB,WAAW;gBACT,cAAc;gBACd,cAAc;gBACd,SAAS;gBACT,WAAW;gBAEX,IAAI;oBACF,MAAM,WAAW,cAAc,EAAE;oBAEjC,IAAI,UAAU;wBACZ,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,qBAAqB;4BAC5C,SAAS,KAAK,EAAE;4BAChB,WAAW,KAAK,IAAI;4BACpB;wBACF;oBACF;oBAEA,IAAI,cAAc,IAAI,KAAK,WAAW;wBACpC,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,sBAAsB;4BAC7C,SAAS,KAAK,EAAE;4BAChB,WAAW,KAAK,IAAI;4BACpB,OAAO;wBACT;oBACF;oBAEA,IACE,cAAc,IAAI,KAAK,cACvB,cAAc,IAAI,KAAK,gBACvB,cAAc,IAAI,KAAK,mBACvB;wBACA,IAAI;4BACF,MAAM,gBAAgB,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,4BAA4B;gCACzE,QAAQ,cAAc,EAAE;gCACxB,UAAU,cAAc,IAAI;4BAC9B;4BAEA,IAAI,cAAc,IAAI,CAAC,OAAO,EAAE;gCAC9B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,qBAAqB,EAAE,cAAc,IAAI,CAAC,+BAA+B,CAAC;4BAC3F,OAAO;gCACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,2BAA2B,EAAE,cAAc,IAAI,CAAC,EAAE,EAAE,cAAc,IAAI,CAAC,OAAO,IAAI,iBAAiB;4BAExG;wBACF,EAAE,OAAO,UAAe;4BACtB,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,2BAA2B,EAAE,cAAc,IAAI,CAAC,EAAE,EAAE,SAAS,QAAQ,EAAE,MAAM,WAAW,SAAS,OAAO,EAAE;wBAE/G;oBACF;gBACF,EAAE,OAAO,KAAU;oBACjB,QAAQ,KAAK,CAAC,6CAA6C;gBAC7D;gBAEA;YACF,GAAG;QACL;IACF;IAGA,MAAM,iBAAiB;QACrB,IAAI;YACF,IAAI,CAAC,MAAM;gBACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,MAAM,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;gBAC9D,SAAS,KAAK,EAAE;gBAChB,WAAW,KAAK,IAAI;YACtB;YAEA,IAAI,KAAK,MAAM,SAAS;gBACtB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,MAAM,WAAW;YACpC;QACF,EAAE,OAAO,KAAU;YACjB,MAAM,kBAAkB,KAAK,UAAU,MAAM,WAAW,KAAK,WAAW;YACxE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE;;0BACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,YAAY;oBAAE,OAAO;oBAAM,WAAW;gBAA8B;gBACpE,UAAU;oBAAE,OAAO;gBAAK;gBACxB,SAAS,IAAM,UAAU;gBACzB,WAAU;0BACX;;;;;;0BAID,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;8BAET,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;4BAAK,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,OAAO;4BAAG,GAAG;wBAAE;wBAC1B,MAAM;4BAAE,OAAO;4BAAK,GAAG;wBAAG;wBAC1B,WAAW,CAAC,0HAA0H,EAAE,WACpI,+CACA,uFACA;wBACJ,SAAS,CAAC,IAAM,EAAE,eAAe;;0CAEjC,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCACC,OAAM;oCACN,WAAU;oCACV,MAAK;oCACL,SAAQ;oCACR,QAAO;8CAEP,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAIzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gDACR,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAAe;;;;;;oDAAkB;;;;;;;;;;;;;kDAIrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,yLAAA,CAAA,kBAAe;0DACb,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;wDAAI,OAAO;oDAAI;oDACzC,SAAS;wDAAE,SAAS;wDAAG,GAAG;wDAAG,OAAO;oDAAE;oDACtC,MAAM;wDAAE,SAAS;wDAAG,GAAG;wDAAI,OAAO;oDAAI;oDACtC,YAAY;wDAAE,UAAU;wDAAK,MAAM;wDAAU,QAAQ;oDAAI;oDACzD,WAAU;8DAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,OAAO;4DAAK,SAAS;wDAAE;wDAClC,SAAS;4DAAE,OAAO;4DAAG,SAAS;wDAAE;wDAChC,YAAY;4DAAE,OAAO;4DAAK,MAAM;4DAAU,QAAQ;wDAAI;wDACtD,WAAU;;0EAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAU;gEACV,YAAY;oEAAE,OAAO;oEAAK,QAAQ;gEAAE;gEACpC,YAAY;oEAAE,MAAM;oEAAU,WAAW;gEAAI;0EAE7C,cAAA,8OAAC,6HAAA,CAAA,UAAK;oEACJ,KAAK,MAAM,KAAK;oEAChB,KAAK,MAAM,IAAI;oEACf,IAAI;oEACJ,WAAU;oEACV,WAAU;oEACV,QAAQ;oEACR,SAAS;wEACP,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,MAAM,KAAK,EAAE;oEACtD;;;;;;;;;;;0EAGJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gEACR,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAG;gEAC7B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,YAAY;oEAAE,OAAO;gEAAI;gEACzB,WAAU;;oEAET,MAAM,IAAI,KAAK,0BAA0B,UAAU;oEAAW;oEAAE,MAAM,IAAI;;;;;;;0EAE7E,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gEACP,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAG;gEAC7B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,YAAY;oEAAE,OAAO;gEAAI;gEACzB,WAAU;0EAET,mBAAmB,CAAC,MAAM,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;0DAO1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAAC,WAAU;;kEACpB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,QAAQ;4DAAS;4DAC5B,YAAY;gEAAE,MAAM;gEAAS,MAAM;gEAAU,UAAU;4DAAK;4DAC5D,WAAU;4DACV,OAAO;gEACL,UAAU,WAAW,UAAU;gEAC/B,WAAW,WAAW,UAAU;4DAClC;;gEAEC,CAAC,2BACA,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wEACJ,KAAI;wEACJ,KAAK,QAAQ,MAAM,IAAI,GAAG;wEAC1B,IAAI;wEACJ,WAAU;wEACV,WAAU;wEACV,SAAS;4EACP,cAAc;wEAChB;wEACA,QAAQ;;;;;;;;;;yFAIZ,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;sFAA6I;;;;;;;;;;;;;;;;8EAMlK,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oEACZ,SAAS,CAAC,aAAc,aAAa,YAAY,IAAM,qBAAqB,QAAS;oEACrF,YAAY,CAAC,aAAa;wEAAE,OAAO;oEAAK,IAAI,CAAC;oEAC7C,UAAU,CAAC,aAAa;wEAAE,OAAO;oEAAK,IAAI,CAAC;oEAC3C,SAAS;wEACP,QAAQ,aAAa,CAAC,WAAW;wEACjC,OAAO,aAAa,IAAI;oEAC1B;oEACA,WAAW,CAAC,iRAAiR,EAAE,WAAW,sBAAsB,+CAC5T;oEACJ,UAAU;8EAEV,cAAA,8OAAC;wEAAK,WAAW,CAAC,iEAAiE,EAAE,WAAW,eAAe,0BAC3G;kFACD,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4EACV,SAAS;gFAAE,QAAQ;4EAAI;4EACvB,YAAY;gFAAE,UAAU;gFAAG,QAAQ;gFAAU,MAAM;4EAAS;4EAC5D,WAAU;sFACX;;;;;mFAGC,aAAa,eAAe;;;;;;;;;;;;;;;;;;;;;;oDAMvC,yBACC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4DACZ,SAAS;4DACT,WAAU;4DACV,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,UAAU;gEAAE,OAAO;4DAAK;sEACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BASV,SAAS,MAAM,IAAI,KAAK,2BACvB,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,GAAG,CAAC;4CAAK,SAAS;wCAAE;wCAC/B,SAAS;4CACP,GAAG;4CACH,SAAS;gDAAC;gDAAG;gDAAG;6CAAE;4CAClB,GAAG,KAAK,MAAM,KAAK,cAAc,cAAc;wCACjD;wCACA,YAAY;4CAAE,UAAU;4CAAG,OAAO,IAAI;4CAAK,QAAQ;wCAAS;wCAC5D,WAAU;wCACV,OAAO;4CAAE,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAAC;kDAEzC,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,WAAU;;;;;;uCAjBP;;;;;;;;;;;;;;;;;;;;;;;;;;0BA8BrB,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAmB,cAAc;0BAClD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;;wCAAC;sDACL,8OAAC;sDAAO;;;;;;wCAAiB;;;;;;;;;;;;;sCAG9C,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;wCACP,qBAAqB;wCACrB;oCACF;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;AASb;uCAEe", "debugId": null}}, {"offset": {"line": 5046, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/mock-exam-card/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport Header from \"@/app-components/Header\";\r\nimport Footer from \"@/app-components/Footer\";\r\nimport Image from \"next/image\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { getMockExamTopThreeStudents } from \"@/services/LeaderboardUserApi\";\r\nimport MockExamButton from \"../mock-test/mockExamButton\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport SpinningWheel from \"@/components/SpinningWheel\";\r\n\r\n// import { BookCheck } from \"lucide-react\";\r\n// import WeeklyExamButton from \"../mock-test/weeklyExam\";\r\n\r\nconst Page = () => {\r\n    const router = useRouter();\r\n\r\n    let studentId: string | null = null;\r\n    try {\r\n        const data = localStorage.getItem(\"student_data\");\r\n        studentId = data ? JSON.parse(data).id : null;\r\n    } catch {\r\n        studentId = null;\r\n    }\r\n\r\n    const [top3, setTop3] = useState<any[]>([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState<string | null>(null);\r\n    const [isOpen, setIsOpen] = useState(false);\r\n\r\n    useEffect(() => {\r\n        const fetchTop3 = async () => {\r\n            try {\r\n                setLoading(true);\r\n                const response = await getMockExamTopThreeStudents();\r\n                if (response.success) setTop3(response.data.data);\r\n                else setError(response.error);\r\n            } catch (err: any) {\r\n                setError(`Failed to load top performers ${err.message}`);\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n        fetchTop3();\r\n    }, []);\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-black text-white\">\r\n            <Header />\r\n\r\n            <div className=\"fixed right-0 top-10 z-50\">\r\n                <div\r\n                    onClick={() => setIsOpen(true)}\r\n                    className=\"shadow-lg cursor-pointer transform -rotate-90 origin-bottom-right bg-[#FD904B] text-white px-4 py-2 rounded-t-lg\"\r\n                >\r\n                    🎯 Spin the Wheel\r\n                </div>\r\n\r\n                {isOpen && (\r\n                    <div className=\"fixed inset-0 bg-black/30 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-y-auto\">\r\n                        <div className=\"relative w-full max-w-4xl mx-auto\">\r\n                            <div className=\"bg-white dark:bg-gray-100 rounded-xl shadow-xl overflow-hidden\">\r\n                                <SpinningWheel isOpen={isOpen} setIsOpen={setIsOpen} />\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            <section className=\"relative py-10 sm:py-16 px-3 sm:px-8\">\r\n                <div className=\"max-w-6xl mx-auto relative z-10\">\r\n                    <div className=\"text-center mb-10 sm:mb-12\">\r\n                        <h2 className=\"text-2xl sm:text-4xl md:text-5xl font-extrabold tracking-wide mb-3\">\r\n                            🎉 Yesterday&apos;s Top Performers\r\n                        </h2>\r\n                        <p className=\"text-gray-400 text-sm sm:text-base md:text-lg max-w-2xl mx-auto\">\r\n                            The elite students who crushed the challenge and topped the leaderboard!\r\n                        </p>\r\n                    </div>\r\n\r\n                    {loading && <p className=\"text-center text-gray-500\">Loading top performers...</p>}\r\n                    {error && <p className=\"text-center text-red-500\">{error}</p>}\r\n\r\n                    <div className=\"relative h-auto sm:h-[340px] flex flex-wrap sm:flex-nowrap items-end justify-center gap-4 sm:gap-6\">\r\n                        {[1, 0, 2].map((rankIndex, i) => {\r\n                            const student = top3[rankIndex];\r\n                            if (!student) return null;\r\n\r\n                            const heights = [\"h-3/4\", \"h-5/6\", \"h-2/3\"];\r\n                            const medals = [\"🥈\", \"🥇\", \"🥉\"];\r\n                            const accentGlow = i === 1 ? \"shadow-[0_0_20px_#FD904B]\" : \"shadow-lg\";\r\n\r\n                            return (\r\n                                <motion.div\r\n                                    key={student.rank}\r\n                                    initial={{ opacity: 0, y: 40 }}\r\n                                    animate={{ opacity: 1, y: 0 }}\r\n                                    transition={{ duration: 0.4, delay: i * 0.2 }}\r\n                                    className={`w-full ${heights[i]} bg-neutral-900 p-10 ${accentGlow} rounded-t-2xl flex flex-col items-center justify-end pb-6 relative border border-neutral-700 hover:scale-105 transition-all`}\r\n                                >\r\n                                    <div className=\"absolute -top-5 text-3xl sm:text-4xl z-10\">{medals[i]}</div>\r\n                                    <div className=\"mb-4\">\r\n                                        <div className=\"relative w-20 h-20 sm:w-24 sm:h-24 rounded-full overflow-hidden border-4 border-white shadow-lg\">\r\n                                            {student.profilePhoto ? (\r\n                                                <Image\r\n                                                    src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${student.profilePhoto.replace(/\\\\/g, \"/\")}`}\r\n                                                    alt={student.firstName}\r\n                                                    width={96}\r\n                                                    height={96}\r\n                                                    className=\"w-full h-full object-cover\"\r\n                                                />\r\n                                            ) : (\r\n                                                <div className=\"w-full h-full bg-gray-700 flex items-center justify-center text-lg sm:text-xl font-bold text-white\">\r\n                                                    {student.firstName?.[0] || \"U\"}\r\n                                                </div>\r\n                                            )}\r\n                                        </div>\r\n                                    </div>\r\n                                    <div className=\"text-center px-2 sm:px-4\">\r\n                                        <h3 className=\"font-bold text-white text-xs sm:text-base truncate\">\r\n                                            {student.firstName} {student.lastName}\r\n                                        </h3>\r\n                                        <div className=\"flex justify-center gap-1 mt-2 flex-wrap\">\r\n                                            <span className=\"bg-[#FD904B]/20 text-white text-xs px-2 py-1 rounded-full\">\r\n                                                ⚡ {student.score}\r\n                                            </span>\r\n                                            <span className=\"bg-[#FD904B]/20 text-white text-xs px-2 py-1 rounded-full\">\r\n                                                🔥 {student.streakCount}\r\n                                            </span>\r\n                                        </div>\r\n                                    </div>\r\n                                </motion.div>\r\n                            );\r\n                        })}\r\n                    </div>\r\n                    <div className=\"flex justify-center mb-12 mt-12\">\r\n                        <MockExamButton />\r\n                    </div>\r\n\r\n                    <div className=\"flex flex-col sm:flex-row justify-center gap-3 sm:gap-4\">\r\n                        <button\r\n                            onClick={() => router.push(\"/Leader-Board\")}\r\n                            className=\"bg-black hover:bg-neutral-800 border-2 border-[#FD904B] hover:border-[#E88143] text-white px-5 py-2 sm:px-6 sm:py-3 rounded-lg font-bold text-sm sm:text-base transition-all\"\r\n                        >\r\n                            🏆 Leaderboard\r\n                        </button>\r\n                        {\r\n                            studentId && (\r\n                                <button\r\n                                    onClick={() => router.push(`/mock-exam-result/${studentId}`)}\r\n                                    className=\"bg-black hover:bg-neutral-800 border-2 border-[#FD904B] hover:border-[#E88143] text-white px-5 py-2 sm:px-6 sm:py-3 rounded-lg font-bold text-sm sm:text-base transition-all\"\r\n                                >\r\n                                    📊 My Results\r\n                                </button>\r\n                            )\r\n                        }\r\n\r\n                    </div>\r\n                </div>\r\n            </section>\r\n\r\n            <section className=\"relative py-12 px-3 sm:px-6 bg-white text-black rounded-t-3xl\">\r\n                <div className=\"max-w-4xl mx-auto relative\">\r\n                    <div className=\"text-center mb-1\">\r\n                        <span className=\"text-xs font-bold tracking-widest text-[#FD904B] uppercase\">\r\n                            Knowledge Challenge\r\n                        </span>\r\n                    </div>\r\n\r\n                    <h2 className=\"text-2xl sm:text-4xl font-extrabold text-center mb-6\">\r\n                        Daily Quiz\r\n                    </h2>\r\n\r\n                    <p className=\"text-sm sm:text-lg text-center max-w-2xl mx-auto mb-10 leading-relaxed text-gray-700\">\r\n                        Test your skills in our fast-paced daily challenge. Earn rewards,\r\n                        build streaks, and dominate the leaderboards!\r\n                    </p>\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ duration: 0.6, delay: 0.4 }}\r\n                        className=\"mt-4 mb-8 mx-auto w-full max-w-3xl bg-neutral-900 border border-neutral-700 rounded-xl p-4 sm:p-6 shadow-lg flex flex-col sm:flex-row items-center justify-between gap-4\"\r\n                    >\r\n                        <div className=\"flex items-center gap-3 text-center sm:text-left\">\r\n                            <span className=\"text-2xl\">🛍️</span>\r\n                            <p className=\"text-sm sm:text-lg font-semibold text-white\">\r\n                                The <span className=\"text-[#FD904B]\">Store</span> is now <strong>LIVE!</strong> Redeem your coins for exciting rewards!\r\n                            </p>\r\n                        </div>\r\n                        <Button\r\n                            className=\"bg-[#FD904B] hover:bg-orange-600 text-white font-semibold rounded-lg px-4 py-2 sm:px-6 sm:py-2.5 transition-all\"\r\n                            onClick={() => router.push(\"/store\")}\r\n                        >\r\n                            Visit Store\r\n                        </Button>\r\n                    </motion.div>\r\n\r\n                    <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-4 mb-12\">\r\n                        {[\r\n                            { icon: \"📝\", text: \"10 Questions\" },\r\n                            { icon: \"⏱️\", text: \"8 Minutes\" },\r\n                            { icon: \"💰\", text: \"25 Coins\" },\r\n                            { icon: \"🔥\", text: \"Medium Level\" },\r\n                        ].map((item, i) => (\r\n                            <div\r\n                                key={i}\r\n                                className=\"bg-white border border-neutral-200 rounded-xl p-5 text-center shadow hover:scale-105 transition-all min-w-[130px]\"\r\n                            >\r\n                                <div className=\"text-2xl sm:text-3xl mb-3\">{item.icon}</div>\r\n                                <div className=\"font-bold text-sm sm:text-base\">{item.text}</div>\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n                </div>\r\n            </section>\r\n\r\n            <section className=\"relative py-10 px-3 sm:px-6 bg-neutral-900 text-white\">\r\n                <div className=\"max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12\">\r\n                    <div className=\"bg-neutral-800 p-6 sm:p-8 rounded-2xl shadow-xl border border-neutral-700\">\r\n                        <h3 className=\"text-2xl sm:text-3xl font-bold text-[#FD904B] mb-6 flex items-center\">\r\n                            <span className=\"mr-3\">🧾</span> Rules & Rewards\r\n                        </h3>\r\n                        <ul className=\"space-y-3 sm:space-y-4 text-gray-200\">\r\n                            {[\r\n                                { icon: \"🕒\", text: \"1 attempt per day\" },\r\n                                { icon: \"💰\", text: \"Coins based on score\" },\r\n                                { icon: \"🔥\", text: \"Daily streaks = bonus coins\" },\r\n                                { icon: \"🏆\", text: \"Top 3 win stationery prizes\" },\r\n                            ].map((item, i) => (\r\n                                <li key={i} className=\"flex items-start text-sm sm:text-lg\">\r\n                                    <span className=\"text-xl sm:text-2xl mr-3\">{item.icon}</span>\r\n                                    <span>{item.text}</span>\r\n                                </li>\r\n                            ))}\r\n                        </ul>\r\n\r\n                        <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-3 mt-6 sm:mt-8\">\r\n                            {[\r\n                                \"50% : 0 coins\",\r\n                                \"60% : 1 coin\",\r\n                                \"70% : 2 coins\",\r\n                                \"80% : 3 coins\",\r\n                                \"90% : 4 coins\",\r\n                                \"100% : 5 coins\",\r\n                            ].map((reward, i) => (\r\n                                <div\r\n                                    key={i}\r\n                                    className=\"bg-neutral-900 border text-white border-[#FD904B] rounded-lg p-2 sm:p-3 text-xs sm:text-sm font-bold text-center shadow-sm\"\r\n                                >\r\n                                    {reward}\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className=\"bg-neutral-800 p-6 sm:p-8 rounded-2xl shadow-xl border border-neutral-700\">\r\n                        <h3 className=\"text-2xl sm:text-3xl font-bold text-[#FD904B] mb-8 flex items-center\">\r\n                            <span className=\"mr-3\">🏅</span> Achievement Badges\r\n                        </h3>\r\n                        <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-6\">\r\n                            {[\r\n                                { label: \"100 coins\", badge: \"/scholer.svg\" },\r\n                                { label: \"500 coins\", badge: \"/Mastermind.svg\" },\r\n                                { label: \"1000 coins\", badge: \"/Achiever.svg\" },\r\n                                { label: \"30 days\", badge: \"/Perfect Month.svg\" },\r\n                                { label: \"365 days\", badge: \"/Perfect Year.svg\" },\r\n                                { label: \"Daily streak\", badge: \"/Streak.svg\" },\r\n                            ].map((item, i) => (\r\n                                <div key={i} className=\"text-center\">\r\n                                    <div className=\"w-16 h-16 sm:w-20 sm:h-20 mx-auto bg-neutral-800 rounded-full shadow-lg mb-3 overflow-hidden\">\r\n                                        <Image\r\n                                            src={item.badge}\r\n                                            alt={item.label}\r\n                                            width={80}\r\n                                            height={80}\r\n                                            className=\"object-cover w-full h-full\"\r\n                                        />\r\n                                    </div>\r\n                                    <p className=\"text-xs sm:text-sm font-medium\">{item.label}</p>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </section>\r\n\r\n            <section className=\"relative py-12 px-3 sm:px-6 bg-white text-black\">\r\n                <div className=\"max-w-4xl mx-auto\">\r\n                    <h3 className=\"text-2xl sm:text-3xl font-bold text-[#FD904B] mb-8 sm:mb-10 text-center\">\r\n                        🔥 Streak Bonuses\r\n                    </h3>\r\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6\">\r\n                        {[\r\n                            { icon: \"🔥\", title: \"Daily Streak\", reward: \"+1 coin/day\", note: \"Maintain your streak!\" },\r\n                            { icon: \"🏆\", title: \"Weekly Challenge\", reward: \"+5 coins\", note: \"Complete Sunday Challenge!\" },\r\n                            { icon: \"💯\", title: \"Perfect Score\", reward: \"+3 coins\", note: \"Get all answers right!\" },\r\n                        ].map((item, i) => (\r\n                            <div key={i} className=\"bg-white border border-neutral-200 p-4 sm:p-6 rounded-xl shadow hover:scale-105 transition-all text-center\">\r\n                                <div className=\"text-[#FD904B] text-3xl sm:text-4xl mb-3 sm:mb-4\">{item.icon}</div>\r\n                                <h4 className=\"text-lg sm:text-xl font-bold mb-1\">{item.title}</h4>\r\n                                <p className=\"text-gray-700 text-sm sm:text-base\">{item.reward}</p>\r\n                                <div className=\"mt-3 sm:mt-4 bg-[#FD904B]/20 text-[#FD904B] text-xs sm:text-sm font-medium px-3 py-1 rounded-full inline-block\">\r\n                                    {item.note}\r\n                                </div>\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n                </div>\r\n            </section>\r\n\r\n            <Footer />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Page;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;;AAYA,4CAA4C;AAC5C,0DAA0D;AAE1D,MAAM,OAAO;IACT,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,IAAI,YAA2B;IAC/B,IAAI;QACA,MAAM,OAAO,aAAa,OAAO,CAAC;QAClC,YAAY,OAAO,KAAK,KAAK,CAAC,MAAM,EAAE,GAAG;IAC7C,EAAE,OAAM;QACJ,YAAY;IAChB;IAEA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,YAAY;YACd,IAAI;gBACA,WAAW;gBACX,MAAM,WAAW,MAAM,CAAA,GAAA,qIAAA,CAAA,8BAA2B,AAAD;gBACjD,IAAI,SAAS,OAAO,EAAE,QAAQ,SAAS,IAAI,CAAC,IAAI;qBAC3C,SAAS,SAAS,KAAK;YAChC,EAAE,OAAO,KAAU;gBACf,SAAS,CAAC,8BAA8B,EAAE,IAAI,OAAO,EAAE;YAC3D,SAAU;gBACN,WAAW;YACf;QACJ;QACA;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC,mIAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBACG,SAAS,IAAM,UAAU;wBACzB,WAAU;kCACb;;;;;;oBAIA,wBACG,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC,mIAAA,CAAA,UAAa;oCAAC,QAAQ;oCAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9D,8OAAC;gBAAQ,WAAU;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAG,WAAU;8CAAqE;;;;;;8CAGnF,8OAAC;oCAAE,WAAU;8CAAkE;;;;;;;;;;;;wBAKlF,yBAAW,8OAAC;4BAAE,WAAU;sCAA4B;;;;;;wBACpD,uBAAS,8OAAC;4BAAE,WAAU;sCAA4B;;;;;;sCAEnD,8OAAC;4BAAI,WAAU;sCACV;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,WAAW;gCACvB,MAAM,UAAU,IAAI,CAAC,UAAU;gCAC/B,IAAI,CAAC,SAAS,OAAO;gCAErB,MAAM,UAAU;oCAAC;oCAAS;oCAAS;iCAAQ;gCAC3C,MAAM,SAAS;oCAAC;oCAAM;oCAAM;iCAAK;gCACjC,MAAM,aAAa,MAAM,IAAI,8BAA8B;gCAE3D,qBACI,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAEP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,IAAI;oCAAI;oCAC5C,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,WAAW,4HAA4H,CAAC;;sDAE/L,8OAAC;4CAAI,WAAU;sDAA6C,MAAM,CAAC,EAAE;;;;;;sDACrE,8OAAC;4CAAI,WAAU;sDACX,cAAA,8OAAC;gDAAI,WAAU;0DACV,QAAQ,YAAY,iBACjB,8OAAC,6HAAA,CAAA,UAAK;oDACF,KAAK,gEAA0C,QAAQ,YAAY,CAAC,OAAO,CAAC,OAAO,MAAM;oDACzF,KAAK,QAAQ,SAAS;oDACtB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;yEAGd,8OAAC;oDAAI,WAAU;8DACV,QAAQ,SAAS,EAAE,CAAC,EAAE,IAAI;;;;;;;;;;;;;;;;sDAK3C,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAG,WAAU;;wDACT,QAAQ,SAAS;wDAAC;wDAAE,QAAQ,QAAQ;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAK,WAAU;;gEAA4D;gEACrE,QAAQ,KAAK;;;;;;;sEAEpB,8OAAC;4DAAK,WAAU;;gEAA4D;gEACpE,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;mCAjC9B,QAAQ,IAAI;;;;;4BAuC7B;;;;;;sCAEJ,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC,6IAAA,CAAA,UAAc;;;;;;;;;;sCAGnB,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCACG,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;8CACb;;;;;;gCAIG,2BACI,8OAAC;oCACG,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,kBAAkB,EAAE,WAAW;oCAC3D,WAAU;8CACb;;;;;;;;;;;;;;;;;;;;;;;0BAUrB,8OAAC;gBAAQ,WAAU;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC;gCAAK,WAAU;0CAA6D;;;;;;;;;;;sCAKjF,8OAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAIrE,8OAAC;4BAAE,WAAU;sCAAuF;;;;;;sCAIpG,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAW;;;;;;sDAC3B,8OAAC;4CAAE,WAAU;;gDAA8C;8DACnD,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;gDAAY;8DAAQ,8OAAC;8DAAO;;;;;;gDAAc;;;;;;;;;;;;;8CAGvF,8OAAC,kIAAA,CAAA,SAAM;oCACH,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC;8CAC9B;;;;;;;;;;;;sCAKL,8OAAC;4BAAI,WAAU;sCACV;gCACG;oCAAE,MAAM;oCAAM,MAAM;gCAAe;gCACnC;oCAAE,MAAM;oCAAM,MAAM;gCAAY;gCAChC;oCAAE,MAAM;oCAAM,MAAM;gCAAW;gCAC/B;oCAAE,MAAM;oCAAM,MAAM;gCAAe;6BACtC,CAAC,GAAG,CAAC,CAAC,MAAM,kBACT,8OAAC;oCAEG,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAA6B,KAAK,IAAI;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDAAkC,KAAK,IAAI;;;;;;;mCAJrD;;;;;;;;;;;;;;;;;;;;;0BAWzB,8OAAC;gBAAQ,WAAU;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAG,WAAU;;sDACV,8OAAC;4CAAK,WAAU;sDAAO;;;;;;wCAAS;;;;;;;8CAEpC,8OAAC;oCAAG,WAAU;8CACT;wCACG;4CAAE,MAAM;4CAAM,MAAM;wCAAoB;wCACxC;4CAAE,MAAM;4CAAM,MAAM;wCAAuB;wCAC3C;4CAAE,MAAM;4CAAM,MAAM;wCAA8B;wCAClD;4CAAE,MAAM;4CAAM,MAAM;wCAA8B;qCACrD,CAAC,GAAG,CAAC,CAAC,MAAM,kBACT,8OAAC;4CAAW,WAAU;;8DAClB,8OAAC;oDAAK,WAAU;8DAA4B,KAAK,IAAI;;;;;;8DACrD,8OAAC;8DAAM,KAAK,IAAI;;;;;;;2CAFX;;;;;;;;;;8CAOjB,8OAAC;oCAAI,WAAU;8CACV;wCACG;wCACA;wCACA;wCACA;wCACA;wCACA;qCACH,CAAC,GAAG,CAAC,CAAC,QAAQ,kBACX,8OAAC;4CAEG,WAAU;sDAET;2CAHI;;;;;;;;;;;;;;;;sCASrB,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAG,WAAU;;sDACV,8OAAC;4CAAK,WAAU;sDAAO;;;;;;wCAAS;;;;;;;8CAEpC,8OAAC;oCAAI,WAAU;8CACV;wCACG;4CAAE,OAAO;4CAAa,OAAO;wCAAe;wCAC5C;4CAAE,OAAO;4CAAa,OAAO;wCAAkB;wCAC/C;4CAAE,OAAO;4CAAc,OAAO;wCAAgB;wCAC9C;4CAAE,OAAO;4CAAW,OAAO;wCAAqB;wCAChD;4CAAE,OAAO;4CAAY,OAAO;wCAAoB;wCAChD;4CAAE,OAAO;4CAAgB,OAAO;wCAAc;qCACjD,CAAC,GAAG,CAAC,CAAC,MAAM,kBACT,8OAAC;4CAAY,WAAU;;8DACnB,8OAAC;oDAAI,WAAU;8DACX,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACF,KAAK,KAAK,KAAK;wDACf,KAAK,KAAK,KAAK;wDACf,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;;;;;;8DAGlB,8OAAC;oDAAE,WAAU;8DAAkC,KAAK,KAAK;;;;;;;2CAVnD;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAkB9B,8OAAC;gBAAQ,WAAU;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAG,WAAU;sCAA0E;;;;;;sCAGxF,8OAAC;4BAAI,WAAU;sCACV;gCACG;oCAAE,MAAM;oCAAM,OAAO;oCAAgB,QAAQ;oCAAe,MAAM;gCAAwB;gCAC1F;oCAAE,MAAM;oCAAM,OAAO;oCAAoB,QAAQ;oCAAY,MAAM;gCAA6B;gCAChG;oCAAE,MAAM;oCAAM,OAAO;oCAAiB,QAAQ;oCAAY,MAAM;gCAAyB;6BAC5F,CAAC,GAAG,CAAC,CAAC,MAAM,kBACT,8OAAC;oCAAY,WAAU;;sDACnB,8OAAC;4CAAI,WAAU;sDAAoD,KAAK,IAAI;;;;;;sDAC5E,8OAAC;4CAAG,WAAU;sDAAqC,KAAK,KAAK;;;;;;sDAC7D,8OAAC;4CAAE,WAAU;sDAAsC,KAAK,MAAM;;;;;;sDAC9D,8OAAC;4CAAI,WAAU;sDACV,KAAK,IAAI;;;;;;;mCALR;;;;;;;;;;;;;;;;;;;;;0BAa1B,8OAAC,mIAAA,CAAA,UAAM;;;;;;;;;;;AAGnB;uCAEe", "debugId": null}}]}