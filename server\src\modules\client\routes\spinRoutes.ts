import { Router } from "express";
import {
  deductSpinCoins<PERSON>ontroller,
  saveSpinRewardController,
  creditCoinsController,
  checkSpinEligibilityController,
} from "../controllers/spinController";

const spinRouter = Router();

const asyncHandler = (fn: any) => (req: any, res: any, next: any) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

spinRouter.get("/can-spin", asyncHandler(checkSpinEligibilityController));
spinRouter.post("/deduct-spin-coins", deductSpinCoinsController);
spinRouter.post("/save-reward", saveSpinRewardController);
spinRouter.post("/credit-coins", creditCoinsController);

export default spinRouter;
