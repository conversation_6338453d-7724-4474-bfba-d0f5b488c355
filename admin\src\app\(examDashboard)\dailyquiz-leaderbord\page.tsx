"use client";

import React, { useState, useEffect, useMemo } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { DataTable } from "@/app-components/dataTable";
import Pagination from "@/app-components/pagination";
import MockExamLeaderboardFilters, { getMockExamLeaderboard } from "@/services/leaderbordApi";
import Image from 'next/image';
import { AlertTriangle, RefreshCw, X, Filter, CalendarIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import BadgeDisplay from "@/components/ui/badgedisplay";
import { getTerminatedStudentLogs } from "@/services/uwhizMockExamTerminationApi";

interface LeaderboardUser {
    rank: number;
    studentId: string;
    score: number;
    coinEarnings: number;
    streakCount: number;
    firstName: string | null;
    lastName: string | null;
    email: string | null;
    contact?: string | null;
    badge: {
        streakCount: number;
        badges: {
            badgeType: string;
            badgeSrc: string;
            badgeAlt: string;
            count?: number;
        }[];
        badgeType: string | null;
        badgeSrc: string | null;
        badgeAlt: string | null;
    };
    createdAt: string;
}

interface LeaderboardData {
    data: LeaderboardUser[];
    total: number;
}

interface TerminationLog {
    id: string;
    reason: string;
    createdAt: string;
}

const PAGE_SIZE = 10;

export default function SimpleLeaderboardTable() {
    const [leaderboardData, setLeaderboardData] = useState<LeaderboardData>({
        data: [],
        total: 0,
    });
    const [currentPage, setCurrentPage] = useState(1);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [showFilters, setShowFilters] = useState(false);
    const router = useRouter();

    const [filters, setFilters] = useState<MockExamLeaderboardFilters>({
        firstName: '',
        lastName: '',
        email: '',
        score: '',
        coins: '',
        startDate: '',
        endDate: '',

    });

    const [appliedFilters, setAppliedFilters] = useState<MockExamLeaderboardFilters>({});
    const [selectedStudentForTermination, setSelectedStudentForTermination] = useState<LeaderboardUser | null>(null);
    const [terminationLogs, setTerminationLogs] = useState<TerminationLog[]>([]);
    const [terminationLogsLoading, setTerminationLogsLoading] = useState(false);

    const timeframe = "all-time";

    const handleSearch = () => {
        setCurrentPage(1);
        setAppliedFilters({ ...filters });
    };
    const handleStudentDailyQuizeResult = (studentId: string, firstName: string | null, lastName: string | null) => {
        const firstNameParam = firstName || '';
        const lastNameParam = lastName || '';
        router.push(`/student-dailyquize-result/${studentId}?firstName=${encodeURIComponent(firstNameParam)}&lastName=${encodeURIComponent(lastNameParam)}`);
    };
    const handleResetFilters = () => {
        const resetFilters: MockExamLeaderboardFilters = {
            firstName: '',
            lastName: '',
            email: '',
            score: '',
            coins: '',
            startDate: '',
            endDate: '',

        };
        setFilters(resetFilters);
        setAppliedFilters({});
        setCurrentPage(1);
    };
    const handleFilterChange = (field: keyof MockExamLeaderboardFilters, value: string) => {
  setFilters((prev) => ({
    ...prev,
    [field]: value
  }));
};
    const getBadgeIcon = (coinEarnings: number) => {
        if (coinEarnings >= 100 && coinEarnings <= 499) {
            return '/scholer.svg';
        } else if (coinEarnings >= 500 && coinEarnings <= 999) {
            return '/Mastermind.svg';
        } else if (coinEarnings >= 1000) {
            return '/Achiever.svg';
        }
        return null;
    };

    const getBadgeAlt = (coinEarnings: number) => {
        if (coinEarnings >= 100 && coinEarnings <= 499) {
            return 'Scholar Badge';
        } else if (coinEarnings >= 500 && coinEarnings <= 999) {
            return 'Mastermind Badge';
        } else if (coinEarnings >= 1000) {
            return 'Achiever Badge';
        }
        return '';
    };

    const getStudentName = (user: LeaderboardUser) => {
        if (user.firstName && user.lastName) {
            return `${user.firstName} ${user.lastName}`;
        }
        return user.email || `Student ${user.studentId}`;
    };

    const fetchStudentTerminationLogs = async (student: LeaderboardUser) => {
        setSelectedStudentForTermination(student);
        setTerminationLogsLoading(true);
        setTerminationLogs([]);

        try {
            const response = await getTerminatedStudentLogs(student.studentId);

            if (response.success) {
                setTerminationLogs(response.data || []);
            } else {
                console.error("Failed to fetch termination logs:", response.error);
                setTerminationLogs([]);
            }
        } catch (error) {
            console.error("Error fetching termination logs:", error);
            setTerminationLogs([]);
        } finally {
            setTerminationLogsLoading(false);
        }
    };

    const fetchLeaderboardData = async () => {
        setLoading(true);
        setError(null);

        try {
            const response = await getMockExamLeaderboard(timeframe, currentPage, PAGE_SIZE, appliedFilters);

            if (response.success) {
                setLeaderboardData(response.data);
            } else {
                setError(response.error || "Failed to fetch leaderboard data");
                setLeaderboardData({ data: [], total: 0 });
            }
        } catch (err) {
            setError("An unexpected error occurred");
            setLeaderboardData({ data: [], total: 0 });
            console.error("Error", err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchLeaderboardData();
    }, [currentPage, appliedFilters]);

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };

    const hasActiveFilters = Object.values(filters).some(filter => filter && filter.toString().trim() !== '');

    const columns: ColumnDef<LeaderboardUser>[] = useMemo(
        () => [
            {
                header: "Rank",
                accessorKey: "rank",
                cell: ({ row }) => (
                    <div className="font-bold">{row.original.rank}</div>
                ),
            },
            {
                header: "Name",
                accessorKey: "firstName",
                cell: ({ row }) => (
                    <div className="font-semibold">{getStudentName(row.original)}</div>
                ),
            },
            {
                header: "Email",
                accessorKey: "email",
                cell: ({ row }) => (
                    <div>{row.original.email || '-'}</div>
                ),
            },
            {
                header: "Score",
                accessorKey: "score",
                cell: ({ row }) => (
                    <div className="font-semibold">{row.original.score.toLocaleString()}</div>
                ),
            },
            {
                header: "Coins",
                accessorKey: "coinEarnings",
                cell: ({ row }) => (
                    <div className="font-semibold">{row.original.coinEarnings.toLocaleString()}</div>
                ),
            },
            {
                header: "Streak",
                accessorKey: "streakCount",
                cell: ({ row }) => (
                    <div>{row.original.streakCount}</div>
                ),
            },
            {
                header: "Streak Badge",
                accessorKey: "badge",
                cell: ({ row }) => {
                    return (
                        <div className="flex items-center ">
                            <BadgeDisplay badge={row.original.badge} />
                        </div>
                    );
                },
            },
            {
                header: "CoinEarnings Badge",
                accessorKey: "coinEarningsBadge",
                cell: ({ row }) => {
                    const badgeIcon = getBadgeIcon(row.original.coinEarnings);
                    const badgeAlt = getBadgeAlt(row.original.coinEarnings);

                    if (badgeIcon) {
                        return (
                            <div className="flex items-center">
                                <Image
                                    src={badgeIcon}
                                    alt={badgeAlt}
                                    width={32}
                                    height={32}
                                    className="w-8 h-8"
                                    title={badgeAlt}
                                />
                            </div>
                        );
                    }
                    return null;
                },
            },
            {
                id: "termination",
                header: "Termination Logs",
                cell: ({ row }) => {
                    return (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => fetchStudentTerminationLogs(row.original)}
                            className="flex items-center gap-2"
                        >
                            <AlertTriangle className="w-4 h-4" />
                            Termination
                        </Button>
                    );
                },
            },
            {
                header: "Actions",
                id: "actions",
                cell: ({ row }) => (
                    <Button
                        size="sm"
                        onClick={() => handleStudentDailyQuizeResult(
                            row.original.studentId,
                            row.original.firstName,
                            row.original.lastName
                        )}
                    >
                        View Results
                    </Button>
                ),
            },

        ],
        []
    );

    const totalPages = Math.ceil(leaderboardData.total / PAGE_SIZE);

    return (
        <div className="p-6">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h1 className="text-2xl font-bold">Daily Quiz Leaderboard</h1>
                    <p className="text-gray-600 mt-1">All-time student performance data</p>
                </div>
            </div>

            <div className="flex justify-end gap-4 mb-4">
                <Button
                    variant="outline"
                    onClick={() => setShowFilters(!showFilters)}
                    className={`flex items-center gap-2 ${hasActiveFilters ? 'border-primary text-primary' : ''}`}
                    aria-label="Toggle filters"
                >
                    <Filter className="w-4 h-4" />
                    Filters
                </Button>
            </div>

            {showFilters && (
                <Card className="mb-6">
                    <CardContent className="pt-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div>
                                <Label htmlFor="firstName">First Name</Label>
                                <Input
                                    id="firstName"
                                    placeholder="Filter by first name"
                                    value={filters.firstName || ''}
                                    onChange={(e) => handleFilterChange('firstName', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label htmlFor="lastName">Last Name</Label>
                                <Input
                                    id="lastName"
                                    placeholder="Filter by last name"
                                    value={filters.lastName || ''}
                                    onChange={(e) => handleFilterChange('lastName', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label htmlFor="email">Email</Label>
                                <Input
                                    id="email"
                                    placeholder="Filter by email"
                                    value={filters.email || ''}
                                    onChange={(e) => handleFilterChange('email', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label htmlFor="coins">Coin</Label>
                                <Input
                                    id="coins"
                                    placeholder="Filter by coins"
                                    value={filters.coins || ''}
                                    onChange={(e) => handleFilterChange('coins', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label htmlFor="score">Score</Label>
                                <Input
                                    id="score"
                                    type="number"
                                    placeholder="Filter by score"
                                    value={filters.score || ''}
                                    onChange={(e) => handleFilterChange('score', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label htmlFor="startDate">Start Date</Label>
                                <div className="relative">
                                    <input
                                        id="startDate"
                                        type="date"
                                        value={filters.startDate || ''}
                                        onChange={(e) => handleFilterChange('startDate', e.target.value)}
                                        className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900"
                                        placeholder="Select start date"
                                    />
                                    <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                                </div>
                            </div>
                            <div>
                                <Label htmlFor="endDate">End Date</Label>
                                <div className="relative">
                                    <input
                                        id="endDate"
                                        type="date"
                                        value={filters.endDate || ''}
                                        onChange={(e) => handleFilterChange('endDate', e.target.value)}
                                        className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900"
                                        placeholder="Select end date"
                                    />
                                    <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                                </div>
                            </div>
                        </div>

                        <div className="flex gap-2 mt-4">
                            <Button
                                onClick={handleSearch}
                                disabled={loading}
                            >
                                Search
                            </Button>
                            <Button
                                variant="outline"
                                onClick={handleResetFilters}
                                disabled={loading}
                            >
                                Reset
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            )}

            {error && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-600 font-medium">{error}</p>
                </div>
            )}

            <DataTable
                columns={columns}
                data={leaderboardData.data}
                isLoading={loading}
            />

            {leaderboardData.total > 0 && (
                <div className="mt-6">
                    <Pagination
                        page={currentPage}
                        totalPages={totalPages}
                        setPage={handlePageChange}
                        entriesText={`${leaderboardData.data.length} of ${leaderboardData.total} entries`}
                    />
                </div>
            )}

            {leaderboardData.data.length === 0 && !loading && !error && (
                <div className="text-center py-12 bg-white rounded-lg shadow">
                    <div className="text-gray-400 text-6xl mb-4">📊</div>
                    <p className="text-gray-500 text-lg">No leaderboard data available</p>
                    <p className="text-gray-400 text-sm mt-2">Check back later for updated data.</p>
                </div>
            )}

            {/* Termination Logs Modal */}
            {selectedStudentForTermination && (
                <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-auto w-full">
                        <div className="p-4 border-b sticky top-0 bg-white">
                            <div className="flex justify-between items-center">
                                <div>
                                    <h3 className="font-semibold text-lg">
                                        Termination Logs - {selectedStudentForTermination.firstName} {selectedStudentForTermination.lastName}
                                    </h3>
                                    <p className="text-sm text-gray-500">
                                        Student ID: {selectedStudentForTermination.studentId} | Email: {selectedStudentForTermination.email} | Rank: {selectedStudentForTermination.rank}
                                    </p>
                                </div>
                                <div className="flex gap-2">
                                    <Button
                                        variant="outline"
                                        onClick={() => fetchStudentTerminationLogs(selectedStudentForTermination)}
                                        disabled={terminationLogsLoading}
                                    >
                                        <RefreshCw className={`w-4 h-4 mr-2 ${terminationLogsLoading ? "animate-spin" : ""}`} />
                                        Refresh
                                    </Button>
                                    <Button
                                        variant="outline"
                                        onClick={() => {
                                            setSelectedStudentForTermination(null);
                                            setTerminationLogs([]);
                                        }}
                                    >
                                        <X className="w-4 h-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                        <div className="p-4">
                            {terminationLogsLoading ? (
                                <div className="flex justify-center items-center py-12">
                                    <RefreshCw className="w-8 h-8 animate-spin text-primary" />
                                </div>
                            ) : terminationLogs.length > 0 ? (
                                <div className="space-y-4">
                                    <p className="text-sm text-gray-600">
                                        Found {terminationLogs.length} termination logs for this student
                                    </p>
                                    <div className="space-y-3">
                                        {terminationLogs.map((log, index) => (
                                            <div
                                                key={log.id}
                                                className="border rounded-lg p-4 bg-red-50 border-red-200"
                                            >
                                                <div className="flex justify-between items-start">
                                                    <div className="flex-1">
                                                        <div className="flex items-center gap-2 mb-2">
                                                            <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                                                Termination #{index + 1}
                                                            </span>
                                                            <span className="text-sm text-gray-500">
                                                                {new Date(log.createdAt).toLocaleString()}
                                                            </span>
                                                        </div>
                                                        <div className="text-sm">
                                                            <strong>Reason:</strong> {log.reason}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <X className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                                    <p className="text-gray-500">
                                        No termination logs found for this student
                                    </p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}