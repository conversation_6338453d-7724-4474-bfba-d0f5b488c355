"use client";

import React, { useState, useEffect } from "react";
import { DataTable } from "@/app-components/dataTable";
import { ColumnDef } from "@tanstack/react-table";
import { getSpinWinners } from "@/services/spinApi";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import Pagination from "@/app-components/pagination";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { Search, Gift } from "lucide-react";
import { SpinWinner } from "@/lib/types";

const SpinWinnersPage = () => {
  const [winners, setWinners] = useState<SpinWinner[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [search, setSearch] = useState("");
  const [modelType, setModelType] = useState("ALL");
  const [rewardName, setRewardName] = useState("ALL");

  const limit = 10;

  const fetchWinners = async (page: number = 1, filters: any = {}) => {
    setIsLoading(true);
    try {
      const response = await getSpinWinners({
        page,
        limit,
        search: filters.search || search,
        modelType: filters.modelType || modelType,
        rewardName: filters.rewardName || rewardName,
      });

      setWinners(response.data);
      setCurrentPage(response.pagination.currentPage);
      setTotalPages(response.pagination.totalPages);
      setTotalCount(response.pagination.totalCount);

    } catch (error: any) {
      console.error("Error fetching spin winners:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchWinners(1);
  }, []);

  const handleSearch = () => {
    setCurrentPage(1);
    fetchWinners(1, { search, modelType, rewardName });
  };

  const handleReset = () => {
    setSearch("");
    setModelType("ALL");
    setRewardName("ALL");
    setCurrentPage(1);
    fetchWinners(1, { search: "", modelType: "ALL", rewardName: "ALL" });
  };

  const getModelTypeBadge = (type: string) => {
    return type === "STUDENT" ? (
      <Badge variant="default" className="bg-blue-100 text-blue-800">
        Student
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-green-100 text-green-800">
        Class
      </Badge>
    );
  };

  const getRewardTypeBadge = (type: string) => {
    const colors: { [key: string]: string } = {
      PHYSICAL: "bg-purple-100 text-purple-800",
      DIGITAL: "bg-orange-100 text-orange-800",
      COINS: "bg-yellow-100 text-yellow-800",
      UNKNOWN: "bg-gray-100 text-gray-800",
    };

    return (
      <Badge variant="outline" className={colors[type] || colors.UNKNOWN}>
        {type}
      </Badge>
    );
  };

  const columns: ColumnDef<SpinWinner>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => {
        const winner = row.original;
        return (
          <div>
            <div className="font-medium">{winner.name}</div>
            <div className="text-sm text-muted-foreground">{winner.email}</div>
          </div>
        );
      },
    },
    {
      accessorKey: "modelType",
      header: "Type",
      cell: ({ row }) => getModelTypeBadge(row.getValue("modelType")),
    },
    {
      accessorKey: "rewardName",
      header: "Prize Won",
      cell: ({ row }) => {
        const winner = row.original;
        return (
          <div className="flex items-center gap-2">
            {winner.rewardImage && (
              <img 
                src={winner.rewardImage} 
                alt={winner.rewardName}
                className="h-8 w-8 object-contain"
              />
            )}
            <div>
              <div className="font-medium">{winner.rewardName}</div>
              {getRewardTypeBadge(winner.rewardType)}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "wonAt",
      header: "Won At",
      cell: ({ row }) => {
        const date = new Date(row.getValue("wonAt"));
        return (
          <div>
            <div>{format(date, "MMM dd, yyyy")}</div>
            <div className="text-sm text-muted-foreground">
              {format(date, "hh:mm a")}
            </div>
          </div>
        );
      },
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Spin Winners</h1>
          <p className="text-muted-foreground">
            Manage and view all spin wheel winners
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Search</label>
              <Input
                placeholder="Search by name, email, or reward..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">User Type</label>
              <Select value={modelType} onValueChange={setModelType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Types</SelectItem>
                  <SelectItem value="STUDENT">Students</SelectItem>
                  <SelectItem value="CLASS">Classes</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Reward</label>
              <Select value={rewardName} onValueChange={setRewardName}>
                <SelectTrigger>
                  <SelectValue placeholder="Select reward" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Rewards</SelectItem>
                  <SelectItem value="Laptop">Laptop</SelectItem>
                  <SelectItem value="Keychain">Keychain</SelectItem>
                  <SelectItem value="1 notebook">1 Notebook</SelectItem>
                  <SelectItem value="uest super card">UEST Super Card</SelectItem>
                  <SelectItem value="3 coins">3 Coins</SelectItem>
                  <SelectItem value="Better luck next time">Better Luck Next Time</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end gap-2">
              <Button onClick={handleSearch} className="flex-1">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" onClick={handleReset}>
                Reset
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
              <span className="ml-4">Loading winners...</span>
            </div>
          ) : winners.length === 0 ? (
            <div className="text-center p-16 bg-gray-50 rounded-xl border-2 border-dashed border-gray-200">
              <Gift className="h-16 w-16 mx-auto mb-6 text-gray-400" />
              <h3 className="text-xl font-semibold text-gray-700 mb-2">
                No Winners Found
              </h3>
              <p className="text-gray-500">
                No spin winners match your current filters.
              </p>
            </div>
          ) : (
            <DataTable columns={columns} data={winners} isLoading={isLoading} />
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {!isLoading && winners.length > 0 && (
        <Pagination
          page={currentPage}
          totalPages={totalPages}
          setPage={(page) => {
            setCurrentPage(page);
            fetchWinners(page);
          }}
          entriesText={`${totalCount} entries`}
        />
      )}
    </div>
  );
};

export default SpinWinnersPage;
