"use client";

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Camera, CameraOff, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';
import { uploadExamPhoto } from '@/services/examMonitoringApi';
import { getStudentDetail } from '@/services/studentDetailServiceApi';
import * as faceapi from '@vladmandic/face-api';

interface ExamCameraMonitoringProps {
  studentId: string;
  examId: number;
  isExamActive: boolean;
  shouldSavePhoto: boolean;
  onCameraError?: (error: string) => void;
  onCameraStatus?: (isActive: boolean) => void;
  onViolation?: (reason: string) => void;
}

const loadModels = async () => {
  try {
    const MODEL_URL = '/models';
    await Promise.all([
      faceapi.nets.ssdMobilenetv1.loadFromUri(MODEL_URL),
      faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL),
      faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL),
    ]);
  } catch (error: any) {
    throw new Error('Failed to load face-api.js models',error);
  }
};

const getFaceDescriptor = async (source: HTMLImageElement | HTMLVideoElement) => {
  const detection = await faceapi
    .detectSingleFace(source, new faceapi.SsdMobilenetv1Options())
    .withFaceLandmarks()
    .withFaceDescriptor();
  return detection?.descriptor;
};

const isLookingAtScreen = (landmarks: faceapi.FaceLandmarks68) => {
  const leftEye = landmarks.getLeftEye();
  const rightEye = landmarks.getRightEye();
  const nose = landmarks.getNose();
  const eyeMidX = (leftEye[0].x + rightEye[3].x) / 2;
  const noseX = nose[3].x;
  const tiltThreshold = 20;
  return Math.abs(eyeMidX - noseX) < tiltThreshold;
};

const ExamCameraMonitoring: React.FC<ExamCameraMonitoringProps> = ({
  studentId,
  examId,
  isExamActive,
  onCameraError,
  onCameraStatus,
  shouldSavePhoto,
  onViolation,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const [isCameraActive, setIsCameraActive] = useState(false);
  const [cameraError, setCameraError] = useState<string | null>(null);
  const [referenceDescriptor, setReferenceDescriptor] = useState<Float32Array | null>(null);
  const [isFaceApiReady, setIsFaceApiReady] = useState(false);

  const startCamera = useCallback(async () => {
    try {
      setCameraError(null);
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: 640, height: 480, facingMode: 'user' },
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
        setIsCameraActive(true);
      }
    } catch (error: any) {
      const errorMessage = error.name === 'NotAllowedError'
        ? 'Camera access denied'
        : 'Camera not available';
      setCameraError(errorMessage);
      onCameraError?.(errorMessage);
      onCameraStatus?.(false);
      toast.error(errorMessage);
    }
  }, [onCameraError, onCameraStatus]);

  const capturePhoto = useCallback(async () => {
    if (!videoRef.current || !canvasRef.current || !isCameraActive) return;

    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');
      if (!context) return;

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      const photoData = canvas.toDataURL('image/jpeg', 0.7);
       if (shouldSavePhoto) {
        const result = await uploadExamPhoto({ studentId, examId, photoData });
        
        if (!result.success) {
          console.error('Failed to upload photo:', result.error);
        }
      }
    } catch (error) {
      console.error('Failed to capture or upload photo:', error);
    }
  }, [studentId, examId, isCameraActive, shouldSavePhoto]);

  const stopCamera = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsCameraActive(false);
  }, []);

  useEffect(() => {
    const initializeFaceApi = async () => {
      try {
        await loadModels();
        const studentResponse = await getStudentDetail(studentId);

        if (!studentResponse.success || !studentResponse.data?.photo) {
          throw new Error('Failed to fetch student photo');
        }

        const img = new Image();
        img.crossOrigin = 'Anonymous';
        img.src = `${process.env.NEXT_PUBLIC_API_BASE_URL}${studentResponse.data.photo}`;
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = () => reject('Failed to load student photo');
        });

        const descriptor = await getFaceDescriptor(img);
        if (!descriptor) {
          onViolation?.('No face detected in reference photo');
          throw new Error('No face in reference photo');
        }

        setReferenceDescriptor(descriptor);
        setIsFaceApiReady(true);
        onCameraStatus?.(true);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to initialize face recognition';
        onCameraError?.(errorMessage);
        toast.error(errorMessage);
        onCameraStatus?.(true);
      }
    };

    if (isExamActive && !isFaceApiReady) {
      initializeFaceApi();
    }
  }, [studentId, isExamActive, isCameraActive, onCameraError, onCameraStatus, onViolation]);

  useEffect(() => {
    if (isExamActive && !isCameraActive) {
      startCamera();
    } else if (!isExamActive && isCameraActive) {
      stopCamera();
    }
  }, [isExamActive, isCameraActive, startCamera, stopCamera]);

  useEffect(() => {
    if (isCameraActive && isExamActive && isFaceApiReady && referenceDescriptor) {
      intervalRef.current = setInterval(async () => {
        if (!videoRef.current) return;

        const detections = await faceapi
          .detectAllFaces(videoRef.current, new faceapi.SsdMobilenetv1Options())
          .withFaceLandmarks()
          .withFaceDescriptors();

        if (detections.length === 0) {
          onViolation?.('No face detected');
          return;
        }

        if (detections.length > 1) {
          onViolation?.('Multiple faces detected');
          return;
        }

        const currentDescriptor = detections[0].descriptor;
        const distance = faceapi.euclideanDistance(referenceDescriptor, currentDescriptor);
        if (distance > 0.6) {
          onViolation?.('Different student detected');
          return;
        }

        const landmarks = detections[0].landmarks;
        if (!isLookingAtScreen(landmarks)) {
          onViolation?.('Not looking at screen');
          return;
        }

        await capturePhoto();
      }, 10000);

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      };
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isCameraActive, isExamActive, isFaceApiReady, referenceDescriptor, capturePhoto, onViolation]);

  useEffect(() => {
    return () => stopCamera();
  }, [stopCamera]);

  return (
    <div className="fixed top-4 right-4 z-50">
      <div className="bg-black rounded-lg p-2 shadow-lg border-2 border-orange-500">
        <div className="flex items-center gap-2 mb-2">
          {isCameraActive ? (
            <Camera className="w-4 h-4 text-green-500" />
          ) : (
            <CameraOff className="w-4 h-4 text-red-500" />
          )}
          <span className="text-white text-xs font-medium">
            {isCameraActive && !isFaceApiReady ? 'Initializing Face Recognition...' : isCameraActive ? 'Monitoring Active' : 'Camera Inactive'}
          </span>
        </div>

        {cameraError ? (
          <div className="w-32 h-24 bg-red-900 rounded flex items-center justify-center">
            <AlertTriangle className="w-6 h-6 text-red-400" />
          </div>
        ) : (
          <video
            ref={videoRef}
            className="w-32 h-24 rounded object-cover transform scale-x-[-1]"
            autoPlay
            playsInline
            muted
          />
        )}

        {cameraError && (
          <div className="mt-2 text-xs text-red-400 max-w-32">
            {cameraError}
          </div>
        )}
      </div>

      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
};

export default ExamCameraMonitoring;
