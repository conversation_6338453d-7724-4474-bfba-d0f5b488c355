"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { axiosInstance } from "@/lib/axios";
import { toast } from "sonner";
import Image from "next/image";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface Prize {
  name: string;
  image: string;
  type: "item" | "coins" | "nothing";
}
interface SpinningWheelProps {
  isOpen?: boolean;
  setIsOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}
const SpinningWheel = ({ isOpen: externalIsOpen, setIsOpen: externalSetIsOpen }: SpinningWheelProps) => {
  const [isOpenInternal, setIsOpenInternal] = useState(false);
  const isOpen = externalIsOpen !== undefined ? externalIsOpen : isOpenInternal;
  const setIsOpen = externalSetIsOpen || setIsOpenInternal;
  const [isSpinning, setIsSpinning] = useState(false);
  const [prize, setPrize] = useState<Prize | null>(null);
  const [rotation, setRotation] = useState(0);
  const [hasSpun, setHasSpun] = useState(false);
  const [user, setUser] = useState<{ id: string; type: "STUDENT" | "CLASS" } | null>(null);
  const [isEligible, setIsEligible] = useState(false);
  const [windowHeight, setWindowHeight] = useState(500);
  const [windowWidth, setWindowWidth] = useState(500);
  const [imageError, setImageError] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const isMobile = useIsMobile();
  const spinSoundRef = useRef<HTMLAudioElement | null>(null);





  useEffect(() => {
    spinSoundRef.current = new Audio("/spinsound.mp3");
  }, []);

  const prizes: Prize[] = [
    { name: "Laptop", image: "/laptop.svg", type: "item" },
    { name: "Keychain", image: "/keychain.svg", type: "item" },
    { name: "3 coins", image: "/3coins.svg", type: "coins" },
    { name: "uest super card", image: "/uestcard.svg", type: "item" },
    { name: "Better luck next time", image: "/betterluck.svg", type: "nothing" },
    { name: "1 notebook", image: "/1notebook.svg", type: "item" },
  ];

  const prizeWeights = [
    { name: "Better luck next time", weight: 25 },
    { name: "3 coins", weight: 40 },
    { name: "1 notebook", weight: 20 },
    { name: "Keychain", weight: 10 },
    { name: "uest super card", weight: 5 },
    { name: "Laptop", weight: 0 },
  ];

  const selectPrizeByProbability = (): Prize => {
    const totalWeight = prizeWeights.reduce((sum, item) => sum + item.weight, 0);
    const random = Math.random() * totalWeight;

    let currentWeight = 0;
    for (const weightItem of prizeWeights) {
      currentWeight += weightItem.weight;
      if (random <= currentWeight) {
        return prizes.find(prize => prize.name === weightItem.name) || prizes[0];
      }
    }

    return prizes.find(prize => prize.name === "Better luck next time") || prizes[0];
  };

  const encouragingMessages: { [key: string]: string } = {
    "Laptop": "Whoa! You just hit the jackpot and won a shiny new laptop! 🧑‍💻✨",
    "Keychain": "Yay! A cool keychain to show off your spinning skills! 🔑🎉",
    "3 coins": "Yayy! You've bagged 3 golden coins! 💰💰💰",
    "uest super card": "Boom! You've unlocked the UEST Super Card !!! you're a legend! 💳🌟",
    "1 notebook": "Nice! A fresh notebook to doodle, dream, and jot your genius ideas! 📓🖍️",
    "Better luck next time": "Oh no! No prize this time... but don’t give up, superstar! 🌧️💪",
  };

  useEffect(() => {
    const studentRaw = localStorage.getItem("student_data");
    const classRaw = localStorage.getItem("user");
    const student = studentRaw ? JSON.parse(studentRaw) : null;
    const classUser = classRaw ? JSON.parse(classRaw) : null;

    if (student?.id) setUser({ id: student.id, type: "STUDENT" });
    else if (classUser?.id) setUser({ id: classUser.id, type: "CLASS" });
  }, []);

  useEffect(() => {
    const updateWindowDimensions = () => {
      setWindowHeight(window.innerHeight);
      setWindowWidth(window.innerWidth);
    };

    updateWindowDimensions();
    window.addEventListener('resize', updateWindowDimensions);

    return () => window.removeEventListener('resize', updateWindowDimensions);
  }, []);

  const checkSpinEligibility = async () => {
    if (!user) return;
    try {
      const res = await axiosInstance.get("/spin/can-spin", {
        params: {
          modelId: user.id,
          modelType: user.type,
        },
      });
      setIsEligible(res.data.success && res.data.eligible === true);
    } catch (err) {
      console.error("Error checking spin eligibility:", err);
      setIsEligible(false);
    }
  };

  useEffect(() => {
    checkSpinEligibility();
  }, [user]);

  const rewardMap: { [key: string]: string } = {
    "Laptop": "2ee90e37-b176-45ba-991f-7e18d3d0191b",
    "Keychain": "fbec0133-1fff-45ae-b50e-4d5ad2988861",
    "3 coins": "7d52f598-5a08-41d3-8e8d-c1fd0c821e29",
    "uest super card": "986b9573-9875-4ef8-9f6e-ec5e5f92d014",
    "Better luck next time": "e5328123-d443-4285-b600-9b44d55b7b06",
    "1 notebook": "6507cf36-a690-4d1c-bd21-5f4f823b050a",
  };

  const handleClose = () => {
    setIsOpen(false);
    setHasSpun(false);
    setPrize(null);
    setRotation(0);
    checkSpinEligibility();
  };

  const spinWheel = () => {
    if (!user) {
      toast.error("Please login to spin the wheel");
      return;
    }

    if (!isSpinning) {
      setPrize(null);
      setIsSpinning(true);
      setRotation(0);

      spinSoundRef.current?.play().catch(() => { });

      const spinInterval = setInterval(() => {
        setRotation((prev) => prev + 30);
      }, 50);

      const selectedPrize = selectPrizeByProbability();

      setTimeout(async () => {
        clearInterval(spinInterval);
        setIsSpinning(false);
        setPrize(selectedPrize);
        setHasSpun(true);

        try {
          const rewardId = rewardMap[selectedPrize.name];

          if (rewardId) {
            await axiosInstance.post("/spin/save-reward", {
              modelId: user.id,
              modelType: user.type,
              rewardId,
            });
          }

          if (selectedPrize.name === "3 coins") {
            await axiosInstance.post("/spin/credit-coins", {
              modelId: user.id,
              modelType: user.type,
              coins: 3,
            });
          }

          if (
            selectedPrize.name === "Keychain" ||
            selectedPrize.name === "1 notebook" ||
            selectedPrize.name === "uest super card"
          ) {
            try {
              const orderResponse = await axiosInstance.post("/store/create-spin-order", {
                itemId: rewardId,
                itemName: selectedPrize.name,
              });

              if (orderResponse.data.success) {
                toast.success(`🎉 Order created for ${selectedPrize.name}! Check your orders in profile.`);
              } else {
                toast.error(
                  `Failed to create order for ${selectedPrize.name}: ${orderResponse.data.message || "Unknown error"}`
                );
              }
            } catch (orderErr: any) {
              toast.error(
                `Failed to create order for ${selectedPrize.name}: ${orderErr.response?.data?.message || orderErr.message}`
              );
            }
          }
        } catch (err: any) {
          console.error("Failed to log spin reward or credit coins", err);
        }

        checkSpinEligibility();
      }, 4000);
    }
  };


  const payToSpinAgain = async () => {
    try {
      if (!user) {
        toast.error("Please login to spin the wheel");
        return;
      }

      const res = await axiosInstance.post("/spin/deduct-spin-coins", {
        modelId: user.id,
        modelType: user.type,
      });

      if (res?.data?.success) {
        toast.success("10 coins deducted! You can spin again.");
        spinWheel();
      } else {
        toast.error(res?.data?.message || "Failed to deduct coins");
      }
    } catch (err: any) {
      const fallbackMessage = err?.response?.data?.message || err?.message || "Unknown error";
      toast.error(fallbackMessage);
    }
  };

  return (
    <>
      <motion.button
        whileHover={{ scale: 1.05, boxShadow: "0 10px 25px rgba(0,0,0,0.2)" }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsOpen(true)}
        className="px-4 py-2 sm:px-6 sm:py-3 bg-customOrange cursor-pointer text-primary-foreground font-semibold rounded-lg shadow-md transition-colors text-sm sm:text-base"
      >
        🎡 Spin the Wheel!
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-1 xs:p-2 sm:p-4 overflow-hidden"
            onClick={handleClose}
          >
            <motion.div
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
              className={`bg-card/95 backdrop-blur-md rounded-2xl shadow-2xl w-full relative mx-auto border border-border/50 ring-1 ring-primary/20 ${isMobile
                ? "max-w-[95%] p-3 max-h-[95vh] min-h-[480px]"
                : "max-w-[98%] md:max-w-[720px] lg:max-w-[900px] p-4 sm:p-6 max-h-[90vh] min-h-[500px]"
                }`}
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={handleClose}
                className="absolute top-2 right-2 sm:top-4 sm:right-4 text-muted-foreground hover:text-foreground transition-colors"
                aria-label="Close"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 sm:h-6 sm:w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              <div className="flex flex-col h-full">
                <div className="text-center mb-4 sm:mb-6">
                  <motion.h2
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-xl sm:text-2xl md:text-3xl font-bold text-orange-400 mb-2"
                  >
                    Spin & Win! 🎁
                  </motion.h2>
                  <p className="text-black">
                    <span className="text-red-600">Disclaimer:</span> The spinner is for engagement and learning. No real money is involved. Coins have no cash value and cannot be exchanged for real currency.
                  </p>
                </div>

                <div className="flex flex-col lg:flex-row flex-1 gap-4 sm:gap-6">
                  <AnimatePresence>
                    {prize && (
                      <motion.div
                        initial={{ opacity: 0, y: 20, scale: 0.8 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: 20, scale: 0.8 }}
                        transition={{ duration: 0.5, type: "spring", bounce: 0.3 }}
                        className="w-full lg:flex-1 flex flex-col justify-center items-center p-4 sm:p-6 bg-gradient-to-br from-accent/30 to-accent/60 rounded-xl border border-border shadow-lg backdrop-blur-sm"
                      >
                        <motion.div
                          initial={{ scale: 0.5, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ delay: 0.2, type: "spring", bounce: 0.4 }}
                          className="text-center"
                        >
                          <motion.div
                            className="relative w-[78px] h-[78px] sm:w-[98px] sm:h-[98px] md:w-[125px] md:h-[125px] mx-auto mb-3 sm:mb-4"
                            whileHover={{ scale: 1.1, rotate: 5 }}
                            transition={{ type: "spring", stiffness: 300 }}
                          >
                            <Image
                              src={prize.image}
                              alt={prize.name}
                              fill
                              className="object-contain drop-shadow-lg"
                              draggable="false"
                              priority
                              onError={() => {
                                console.error(`Failed to load image: ${prize.image}`);
                              }}
                            />
                          </motion.div>
                          <motion.h3
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.4 }}
                            className="text-lg sm:text-xl md:text-2xl font-bold text-accent-foreground mb-1 sm:mb-2"
                          >
                            {prize.name === "Better luck next time" ? "Oops!" : "You Won "} {prize.name}
                          </motion.h3>
                          <motion.p
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.6 }}
                            className="text-sm sm:text-base md:text-lg text-muted-foreground mb-4 sm:mb-6"
                          >
                            {encouragingMessages[prize.name]}
                          </motion.p>
                        </motion.div>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  <motion.div className="w-full lg:flex-1 flex flex-col items-center justify-center">
                    <div className="relative w-full flex items-center justify-center">
                      <motion.div
                        animate={{ rotate: rotation }}
                        transition={{ type: "tween", ease: "linear", duration: 0.05 }}
                        className="w-full max-w-[320px] sm:max-w-[380px] md:max-w-[420px] lg:max-w-[460px] aspect-square rounded-full flex items-center justify-center bg-background overflow-hidden mx-auto border-4 border-primary relative shadow-xl ring-2 ring-primary/20"
                        style={{
                          minWidth: isMobile ? '280px' : '320px',
                          minHeight: isMobile ? '280px' : '320px'
                        }}
                      >
                        {!imageError ? (
                          <div className="relative w-full h-full">
                            <Image
                              src="/spin3.svg"
                              alt={prize ? prize.name : "Spinning Wheel"}
                              fill
                              className="object-contain"
                              draggable="false"
                              onError={() => {
                                setImageError(true);
                              }}
                              priority
                            />
                          </div>
                        ) : (
                          <div className="w-full h-full relative">
                            <div className="w-full h-full rounded-full border-8 border-primary bg-gradient-to-br from-primary via-primary/80 to-accent flex items-center justify-center">
                              <div className="w-3/4 h-3/4 rounded-full bg-background border-2 border-border flex items-center justify-center text-primary font-bold text-lg shadow-inner">
                                SPIN WHEEL
                              </div>
                            </div>
                          </div>
                        )}
                        <motion.button
                          onClick={!isSpinning ? (isEligible ? spinWheel : () => setShowConfirmDialog(true)) : undefined}
                          whileHover={!isSpinning ? { scale: 1.05 } : {}}
                          whileTap={!isSpinning ? { scale: 0.98 } : {}}
                          animate={{
                            rotate: isSpinning ? -rotation : 0,
                            scale: isSpinning ? 1 : 1
                          }}
                          className={`absolute z-10 rounded-full bg-black shadow-xl shadow-primary/40 flex items-center justify-center border-4 border-background ring-2 ring-primary/30 cursor-pointer hover:from-primary/90 hover:to-primary/70 hover:shadow-2xl hover:shadow-primary/50 transition-all duration-300 ${isMobile ? 'w-[70px] h-[70px]' : 'w-[90px] h-[90px] sm:w-[100px] sm:h-[100px]'
                            }`}
                          disabled={isSpinning}
                        >
                          <span className={`text-primary-foreground font-bold text-center leading-tight px-1 ${isMobile ? 'text-[9px]' : 'text-[10px] sm:text-xs'
                            }`}>
                            {isSpinning ? (
                              <motion.span
                                animate={{ rotate: 360 }}
                                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                className="inline-block"
                              >
                                ⟳
                              </motion.span>
                            ) : isEligible ? "SPIN\nFREE" : "SPIN\n10 COINS"}
                          </span>
                        </motion.button>
                      </motion.div>
                    </div>

                    {hasSpun && (
                      <div className="flex justify-center mt-4 sm:mt-6">
                        <motion.button
                          onClick={handleClose}
                          className="px-8 py-3 rounded-full bg-secondary hover:bg-secondary/90 text-secondary-foreground font-semibold shadow-lg border border-border hover:shadow-xl transition-all duration-200 text-sm sm:text-base"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          CLOSE
                        </motion.button>
                      </div>
                    )}
                  </motion.div>
                </div>
              </div>

              {prize && prize.type === "nothing" && (
                <div className="absolute inset-0 pointer-events-none flex flex-wrap justify-center overflow-hidden">
                  {Array.from({ length: 20 }).map((_, i) => (
                    <motion.div
                      key={i}
                      initial={{ y: -100, opacity: 0 }}
                      animate={{
                        y: windowHeight,
                        opacity: [0, 1, 0],
                        x: Math.random() * windowWidth - windowWidth / 2,
                      }}
                      transition={{ duration: 3, delay: i * 0.1, repeat: Infinity }}
                      className="w-8 h-8 sm:w-10 sm:h-10 absolute"
                      style={{ left: `${Math.random() * 100}%` }}
                    >
                      <Image
                        src="/sadfaceemoji.png"
                        alt="Sad emoji"
                        width={40}
                        height={40}
                        className="w-full h-full object-contain"
                        draggable="false"
                      />
                    </motion.div>
                  ))}
                </div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>



      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Spend 10 coins to spin again?</AlertDialogTitle>
            <AlertDialogDescription>
              This will deduct <strong>10 coins</strong> from your account. Are you sure you want to continue?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                setShowConfirmDialog(false);
                payToSpinAgain();
              }}
            >
              Yes, spin again
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

    </>
  );
};

export default SpinningWheel;