import { axiosInstance } from "@/lib/axios";

interface MockExamLeaderboardFilters {
  firstName?: string;
  lastName?: string;
  email?: string;
  contact?: string;
  score?: string;
  coins?: string;
  startDate?: string;
  endDate?: string;
}

export const getMockExamLeaderboard = async (
  timeframe: string,
  page: number = 1,
  limit: number = 10,
  filters: MockExamLeaderboardFilters = {}
): Promise<any> => {
  try {
    
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (filters.firstName?.trim()) {
      params.append('firstName', filters.firstName.trim());
    }
    if (filters.lastName?.trim()) {
      params.append('lastName', filters.lastName.trim());
    }
    if (filters.email?.trim()) {
      params.append('email', filters.email.trim());
    }
    if (filters.score?.trim()) {
      params.append('score', filters.score.trim());
    }
    if (filters.coins?.trim()) {
      params.append('coins', filters.coins.trim());
        }
    if (filters.startDate?.trim()) {
      params.append('startDate', filters.startDate.trim());    }
    if (filters.endDate?.trim()) {
      params.append('endDate', filters.endDate.trim());
        }

    const fullUrl = `/mock-exam-leaderboard/leaderboard/${timeframe}?${params.toString()}`;

    const response = await axiosInstance.get(fullUrl, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    
    console.log("API Response received:", response.data);
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get mock exam leaderboard data: ${
        error.response?.data?.error || error.message
      }`,
    };
  }
};

export default MockExamLeaderboardFilters;