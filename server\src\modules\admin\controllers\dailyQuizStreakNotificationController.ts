import { Request, Response } from 'express';
import { sendSuccess, sendError } from '@/utils/response';
import { createNotification } from '@/utils/notifications';
import { sendPushNotificationToMultipleUsers } from '@/services/pushNotificationService';
import { UserType, NotificationType } from '@prisma/client';
import prisma from '@/config/prismaClient';

interface StudentStreakData {
  studentId: string;
  streakCount: number;
  lastAttempt: string | null;
  firstName?: string;
  lastName?: string;
}

/**
 * Generate motivational message based on streak count
 */
const generateStreakMessage = (firstName: string, streakCount: number): { title: string; message: string } => {
  if (streakCount > 0) {
    // Motivational messages for continuing streak
    const motivationalMessages = [
      `Amazing ${firstName}! 🔥 You're on a ${streakCount}-day streak! Keep the momentum going with today's quiz!`,
      `Fantastic work ${firstName}! 🌟 ${streakCount} days strong! Don't break the chain - take today's quiz!`,
      `Incredible ${firstName}! 💪 Your ${streakCount}-day streak is inspiring! Continue your journey today!`,
      `Outstanding ${firstName}! ⚡ ${streakCount} consecutive days! You're building an amazing habit!`,
      `Brilliant ${firstName}! 🎯 ${streakCount} days in a row! Your dedication is paying off!`
    ];
    
    const randomMessage = motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)];
    
    return {
      title: `🔥 ${streakCount}-Day Streak!`,
      message: randomMessage
    };
  } else {
    // Reminder messages for starting new streak
    const reminderMessages = [
      `Hi ${firstName}! 🌅 Ready to start a new quiz streak? Take today's daily quiz and begin your journey!`,
      `Good morning ${firstName}! ✨ Every expert was once a beginner. Start your quiz streak today!`,
      `Hello ${firstName}! 🚀 Today is perfect for starting fresh! Take the daily quiz and build your streak!`,
      `Hey ${firstName}! 💡 Knowledge grows with consistency. Begin your daily quiz streak now!`,
      `Hi ${firstName}! 🎯 Small daily steps lead to big achievements. Start your quiz journey today!`
    ];
    
    const randomMessage = reminderMessages[Math.floor(Math.random() * reminderMessages.length)];
    
    return {
      title: '🌟 Start Your Quiz Streak!',
      message: randomMessage
    };
  }
};

/**
 * Process streak data received from uwhiz server and send notifications
 */
export const processStreakNotifications = async (req: Request, res: Response): Promise<void> => {
  try {
    const { studentsWithStreaks } = req.body;

    if (!studentsWithStreaks || !Array.isArray(studentsWithStreaks)) {
      sendError(res, 'Invalid streak data received', 400);
      return;
    }

    if (studentsWithStreaks.length === 0) {
      sendSuccess(res, {
        message: 'No students to process',
        notificationCount: 0,
        errors: []
      });
      return;
    }

    const studentIds = studentsWithStreaks.map((s: StudentStreakData) => s.studentId);

    const students = await prisma.student.findMany({
      where: {
        id: { in: studentIds }
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
      }
    });

    const studentMap = new Map(students.map(s => [s.id, s]));
    const notifications = [];
    const errors: string[] = [];
    const validUserIds: string[] = [];

    for (const streakData of studentsWithStreaks) {
      try {
        const student = studentMap.get(streakData.studentId);

        if (!student) {
          errors.push(`Student not found in database: ${streakData.studentId}`);
          continue;
        }

        const { title, message } = generateStreakMessage(student.firstName, streakData.streakCount);

        const notification = await createNotification({
          userId: student.id,
          userType: UserType.STUDENT,
          type: NotificationType.STUDENT_DAILY_QUIZ_STREAK,
          title,
          message,
          data: {
            actionType: 'OPEN_DAILY_QUIZ',
            streakCount: streakData.streakCount,
            studentName: `${student.firstName} ${student.lastName}`,
            lastAttempt: streakData.lastAttempt,
            timestamp: new Date().toISOString(),
          }
        });

        notifications.push(notification);
        validUserIds.push(student.id);
      } catch (error) {
        const errorMsg = `Failed to create notification for student ${streakData.studentId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
      }
    }

    if (validUserIds.length > 0) {
      try {
        await sendPushNotificationToMultipleUsers({
          userIds: validUserIds,
          userType: UserType.STUDENT,
          title: '🎯 Daily Quiz Reminder',
          message: 'Your daily quiz is ready! Keep building your knowledge streak!',
          data: {
            actionType: 'OPEN_DAILY_QUIZ',
            notificationType: 'STUDENT_DAILY_QUIZ_STREAK',
            timestamp: new Date().toISOString(),
          }
        });
      } catch (pushError) {
        const errorMsg = `Failed to send push notifications: ${pushError instanceof Error ? pushError.message : 'Unknown error'}`;
        errors.push(errorMsg);
      }
    }

    const result = {
      success: true,
      notificationCount: notifications.length,
      errors,
      processedStudents: studentsWithStreaks.length,
      validStudents: validUserIds.length
    };

    sendSuccess(res, result);
  } catch (error) {
    sendError(res, `Internal server error while processing notifications: ${error instanceof Error ? error.message : 'Unknown error'}`, 500);
  }
};

/**
 * Manually trigger daily quiz streak notifications for all students (for testing)
 */
export const triggerDailyQuizStreakNotifications = async (_req: Request, res: Response): Promise<void> => {
  try {
    sendSuccess(res, {
      message: 'Manual trigger is deprecated. Notifications are automatically sent by uwhiz server at 9:00 AM and 6:00 PM daily.',
      deprecated: true
    });
  } catch  {
    sendError(res, 'Internal server error', 500);
  }
};

/**
 * Get status of daily quiz streak notification system
 */
export const getDailyQuizStreakNotificationStatus = async (_req: Request, res: Response): Promise<void> => {
  try {
    const status = {
      systemActive: true,
      lastRunTime: null,
      scheduledTimes: ['09:00 AM IST daily', '06:00 PM IST daily'],
      description: 'Receives and processes streak data from uwhiz server, then sends motivational notifications to students based on their quiz streaks'
    };

    sendSuccess(res, status);
  } catch  {
    sendError(res, 'Internal server error while fetching status', 500);
  }
};

