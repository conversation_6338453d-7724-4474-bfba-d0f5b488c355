import { axiosInstance } from "@/lib/axios";

export const getWeeklyExamResult = async (
  page: number = 1,
  limit: number = 10,
  targetDate?: string
): Promise<any> => {
  try {
    const query = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(targetDate && { targetDate }),
    }).toString();

    const response = await axiosInstance.get(`/weekly-exam/weekly-leaderboard?${query}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });

    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get weekly exam leaderboard: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};