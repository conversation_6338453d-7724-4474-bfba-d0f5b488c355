"use client";

import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useEffect, useState, useRef } from "react";
import { getMockExamResults } from "@/services/mock-exam-resultApi";

export default function MockExamButton() {
  const router = useRouter();
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);
  const [remainingSeconds, setRemainingSeconds] = useState<number | null>(null);
  const [studentId, setStudentId] = useState<string | null>(null);
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const reEnableTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    try {
      const data = localStorage.getItem("student_data");
      const fetchedStudentId = data ? JSON.parse(data).id : null;
      setStudentId(fetchedStudentId);
    } catch (error) {
      console.error("Error retrieving studentId:", error);
      setStudentId(null);
    }
  }, []);

  useEffect(() => {
    const checkExamAttempt = async () => {
      if (!studentId) {
        setIsButtonDisabled(true);
        setRemainingSeconds(null);
        return;
      }

      try {
        const response = await getMockExamResults(studentId, 1, 1, {
          isWeekly: false,
        });
        if (response.success && response.data.data.mockExamResults.length > 0) {
          const latestExam = response.data.data.mockExamResults[0];
          const examDate = new Date(latestExam.createdAt)
            .toISOString()
            .split("T")[0];
          const today = new Date().toISOString().split("T")[0];

          if (examDate === today) {
            setIsButtonDisabled(true);

            const now = new Date();
            const nextMidnight = new Date();
            nextMidnight.setDate(now.getDate() + 1);
            nextMidnight.setHours(0, 0, 0, 0);
            const remainingMs = nextMidnight.getTime() - now.getTime();
            const remainingSec = Math.ceil(remainingMs / 1000);
            setRemainingSeconds(remainingSec > 0 ? remainingSec : null);

            countdownIntervalRef.current = setInterval(() => {
              setRemainingSeconds((prev) => {
                if (prev === null || prev <= 1) {
                  clearInterval(countdownIntervalRef.current!);
                  return null;
                }
                return prev - 1;
              });
            }, 1000);

            reEnableTimeoutRef.current = setTimeout(() => {
              setIsButtonDisabled(false);
              setRemainingSeconds(null);
            }, remainingMs);
          } else {
            setIsButtonDisabled(false);
            setRemainingSeconds(null);
          }
        } else {
          setIsButtonDisabled(false);
          setRemainingSeconds(null);
        }
      } catch (error) {
        console.error("Error checking exam attempt:", error);
        toast.error("Failed to verify exam eligibility.");
        setIsButtonDisabled(true);
        setRemainingSeconds(null);
      }
    };

    checkExamAttempt();

    return () => {
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
      if (reEnableTimeoutRef.current) {
        clearTimeout(reEnableTimeoutRef.current);
      }
    };
  }, [studentId]);

  const handleMockExam = () => {
    if (!studentId) {
      toast.error("Please log in to attempt the exam.");
      router.push("/login");
      return;
    }

    if (isButtonDisabled && remainingSeconds) {
      toast.error(
        `You can attempt the exam again in ${formatRemainingTime()}.`
      );
      return;
    }
    router.push("/mock-test");
  };

  const formatRemainingTime = () => {
    if (remainingSeconds === null) return null;
    const hours = Math.floor(remainingSeconds / 3600);
    const minutes = Math.floor((remainingSeconds % 3600) / 60);
    const seconds = remainingSeconds % 60;
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  return (
    <div className="flex flex-col items-center">
      <Button
        className="w-full max-w-xs bg-black text-white font-bold text-lg p-9 py-4
               border-2 border-[#FD904B] hover:border-[#E88143]
               transition-all duration-300 hover:scale-105"
        onClick={handleMockExam}
        disabled={isButtonDisabled || !studentId}
      >
        🚀 Try Daily Quiz
      </Button>

      {isButtonDisabled && remainingSeconds && (
        <p className="text-center mt-3 text-sm text-gray-400">
          ⏳ Next attempt:{" "}
          <span className="font-semibold">{formatRemainingTime()}</span>
        </p>
      )}
    </div>
  );
}
