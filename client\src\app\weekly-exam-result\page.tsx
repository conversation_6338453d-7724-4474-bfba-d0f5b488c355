"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import Image from "next/image";
import { FaBolt, FaTrophy, FaCrown, FaArrowLeft, FaArrowRight } from "react-icons/fa";
import { toast } from "sonner";
import { getWeeklyExamResult } from "@/services/WeeklyExamService";

interface WeeklyLeaderboardUser {
    rank: number;
    studentId: string;
    studentName: string;
    firstName: string | null;
    lastName: string | null;
    email: string | null;
    score: number;
    duration: number | null;
    profilePhoto?: string | null;
}

interface WeeklyLeaderboardResponse {
    data: WeeklyLeaderboardUser[];
    total: number;
}

export default function WeeklyResultPage() {
    const [leaderboard, setLeaderboard] = useState<WeeklyLeaderboardUser[]>([]);
    const [totalRecords, setTotalRecords] = useState<number>(0);
    const [loadingMore, setLoadingMore] = useState<boolean>(false);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [selectedSunday, setSelectedSunday] = useState<Date>(() => {
        const today = new Date();
        const currentDay = today.getDay();
        const mostRecentSunday = new Date(today);
        mostRecentSunday.setDate(today.getDate() - currentDay);
        mostRecentSunday.setHours(0, 0, 0, 0);
        return mostRecentSunday;
    });

    const MINIMUM_DATE = new Date(2025, 7, 3)

    let loggedInUserId: string | null = null;
    try {
        const data = localStorage.getItem("student_data");
        loggedInUserId = data ? JSON.parse(data).id : null;
    } catch (error: any) {
        console.error("Error retrieving studentId:", error);
        loggedInUserId = null;
    }

    // Utility function to format date as YYYY-MM-DD in local timezone
    const formatDateToString = (date: Date): string => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const fetchWeeklyResult = async (page: number, targetDate: Date): Promise<WeeklyLeaderboardResponse> => {
        try {
            const response = await getWeeklyExamResult(page, 10, formatDateToString(targetDate));
            if (response.success) {
                return response.data as WeeklyLeaderboardResponse;
            } else {
                throw new Error(response.error);
            }
        } catch (err: any) {
            throw new Error("Failed to fetch weekly Result: " + err.message);
        }
    };

    const loadMoreRecords = async () => {
        setLoadingMore(true);
        setError(null);

        const nextPage = currentPage + 1;
        try {
            const response = await fetchWeeklyResult(nextPage, selectedSunday);
            setLeaderboard((prevLeaderboard) => [
                ...prevLeaderboard,
                ...response.data,
            ]);
            setCurrentPage(nextPage);
        } catch (err: unknown) {
            setError(
                "Failed to load more records: " +
                (err instanceof Error ? err.message : String(err))
            );
            toast.error("Failed to load more records");
        }
        setLoadingMore(false);
    };

    useEffect(() => {
        const fetchInitialLeaderboard = async () => {
            setLoading(true);
            setError(null);
            setCurrentPage(1);
            setLeaderboard([]);

            try {
                const response = await fetchWeeklyResult(1, selectedSunday);
                setLeaderboard(response.data);
                setTotalRecords(response.total);
            } catch (error: any) {
                setError("Failed to fetch weekly Result: " + error.message);
                setLeaderboard([]);
                toast.error("Failed to fetch weekly Result");
            }
            setLoading(false);
        };

        fetchInitialLeaderboard();
    }, [selectedSunday]);

    const handlePreviousWeek = () => {
        const prevSunday = new Date(selectedSunday);
        prevSunday.setDate(selectedSunday.getDate() - 7);
        if (prevSunday >= MINIMUM_DATE) {
            setSelectedSunday(prevSunday);
        } else {
            toast.info("Cannot navigate before August 3, 2025");
        }
    };

    const handleNextWeek = () => {
        const nextSunday = new Date(selectedSunday);
        nextSunday.setDate(selectedSunday.getDate() + 7);
        const today = new Date();
        const mostRecentSunday = new Date(today);
        mostRecentSunday.setDate(today.getDate() - today.getDay());
        // Prevent navigating to future weeks
        if (nextSunday <= mostRecentSunday) {
            setSelectedSunday(nextSunday);
        }
    };

    const formatDateRange = (sunday: Date) => {
        const startOfWeek = new Date(sunday);
        const endOfWeek = new Date(sunday);
        endOfWeek.setDate(sunday.getDate() + 6);
        const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric', year: 'numeric' };
        return `${startOfWeek.toLocaleDateString('en-US', options)} - ${endOfWeek.toLocaleDateString('en-US', options)}`;
    };

    const hasMoreRecords = leaderboard.length < totalRecords;

    const renderProfile = (user: WeeklyLeaderboardUser, size: number = 40, isTopThree: boolean = false) => {
        const initials =
            (user.firstName?.charAt(0) || "") + (user.lastName?.charAt(0) || "");

        const isFirst = user.rank === 1 && isTopThree;

        return (
            <div
                style={{ width: size, height: size }}
                className={`flex items-center justify-center rounded-full bg-white text-customOrange font-bold overflow-hidden ${isTopThree ? '' : 'border-2 border-customOrange'} ${isFirst ? 'animate-pulse' : ''}`}
            >
                {user.profilePhoto && user.profilePhoto.trim() !== "" ? (
                    <Image
                        src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${user.profilePhoto}`}
                        alt={`${user.firstName || ""} ${user.lastName || ""}`}
                        width={size}
                        height={size}
                        className="w-full h-full object-cover"
                        style={{ width: size, height: size, objectFit: 'cover' }}
                    />
                ) : (
                    <span className="text-xs sm:text-sm font-semibold">{initials}</span>
                )}
            </div>
        );
    };

    const getLeaderboardSections = () => {
        const topThree = leaderboard.slice(0, 3);
        const others = leaderboard.slice(3);
        if (!loggedInUserId) return { topThree, others };
        const loggedInUser = others.find(user => user.studentId === loggedInUserId);
        const otherUsers = others.filter(user => user.studentId !== loggedInUserId);

        const sortedOthers = loggedInUser ? [loggedInUser, ...otherUsers] : others;

        return { topThree, others: sortedOthers };
    };

    const { topThree, others } = getLeaderboardSections();

    const reorderedTopThree = topThree.length >= 3
        ? [topThree[1], topThree[0], topThree[2]]
        : topThree.length === 2
            ? [topThree[1], topThree[0]]
            : topThree;

    return (
        <>
            <Header />
            <div className="min-h-screen bg-white text-black font-sans py-4 px-4 flex justify-center">
                <div className="w-full max-w-4xl space-y-6 pt-6">
                    {/* Header */}
                    <div className="flex items-center justify-center mb-4">
                        <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-center text-customOrange flex items-center">
                            <FaTrophy className="mr-3 text-customOrange text-xl sm:text-2xl md:text-3xl" />
                            Weekly Quiz Result
                        </h1>
                    </div>

                    <div className="flex flex-row justify-between items-center mb-4 gap-2">
                        <Button
                            onClick={handlePreviousWeek}
                            disabled={selectedSunday.getTime() <= MINIMUM_DATE.getTime()}
                            aria-label="Previous Week"
                            className="px-3 sm:px-4 py-1.5 sm:py-2 bg-customOrange text-white hover:bg-orange-600 disabled:bg-gray-400 flex items-center gap-1.5 text-sm sm:text-base sm:w-auto min-w-[44px]"
                        >
                            <FaArrowLeft className="text-sm sm:text-base" />
                            <span className="hidden sm:inline">Previous</span>
                        </Button>
                        <span className="text-base sm:text-lg font-semibold text-gray-700 text-center">
                            Week of {formatDateRange(selectedSunday)}
                        </span>
                        <Button
                            onClick={handleNextWeek}
                            disabled={selectedSunday.getTime() === new Date().setDate(new Date().getDate() - new Date().getDay())}
                            aria-label="Next Week"
                            className="px-3 sm:px-4 py-1.5 sm:py-2 bg-customOrange text-white hover:bg-orange-600 disabled:bg-gray-400 flex items-center gap-1.5 text-sm sm:text-base sm:w-auto min-w-[44px]"
                        >
                            <span className="hidden sm:inline">Next</span>
                            <FaArrowRight className="text-sm sm:text-base" />
                        </Button>
                    </div>

                    {loading && (
                        <div className="flex justify-center items-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-customOrange"></div>
                        </div>
                    )}

                    {error && (
                        <div className="text-center py-6">
                            <p className="text-red-500 mb-3 text-sm sm:text-base">{error}</p>
                            <Button
                                onClick={() => window.location.reload()}
                                className="bg-customOrange hover:bg-orange-600 text-white px-4 py-2 text-sm sm:text-base"
                            >
                                Try Again
                            </Button>
                        </div>
                    )}

                    {!loading && !error && leaderboard.length === 0 && (
                        <div className="text-center py-8">
                            <p className="text-gray-600 text-base sm:text-lg">No weekly exam results found for this week</p>
                        </div>
                    )}
                    {!loading && !error && leaderboard.length > 0 && (
                        <div className="rounded-xl p-4 sm:p-6">
                            {topThree.length > 0 && (
                                <div className="bg-white rounded-xl p-4 sm:p-6 max-w-full mx-auto mb-6 border border-gray-100">
                                    <div className="flex flex-col md:flex-row justify-center items-center gap-4">
                                        {reorderedTopThree.map((user) => {
                                            const isFirst = user.rank === 1;
                                            const containerClass = isFirst
                                                ? "flex-1 w-full max-w-[200px] sm:max-w-[260px] flex flex-col items-center p-4 sm:p-6 relative"
                                                : "flex-1 w-full max-w-[180px] sm:max-w-[220px] flex flex-col items-center p-3 sm:p-4 border-orange-100";

                                            const profileSize = isFirst ? 80 : 72;
                                            const badgeSize = isFirst ? "w-8 h-8" : "w-7 h-7";
                                            const badgeTextSize = isFirst ? "text-sm" : "text-xs";
                                            const nameClass = isFirst ? "font-bold text-gray-800 text-center mb-2 text-base sm:text-lg" : "font-semibold text-gray-800 text-center mb-1 text-sm sm:text-base";
                                            const scoreClass = isFirst
                                                ? "flex items-center text-customOrange font-semibold text-sm sm:text-base bg-orange-100 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full"
                                                : "flex items-center text-customOrange font-medium px-2 sm:px-3 py-1 rounded-full border border-orange-300 bg-white text-xs sm:text-xs";

                                            return (
                                                <motion.div
                                                    key={user.studentId}
                                                    className={containerClass}
                                                >
                                                    {isFirst ? (
                                                        <div className="flex flex-col items-center">
                                                            <div className="mb-2 sm:mb-3">
                                                                <FaCrown className="text-customOrange w-6 h-6 sm:w-8 sm:h-8 drop-shadow-lg filter brightness-125" />
                                                            </div>
                                                            <div className="relative mb-4 sm:mb-6">
                                                                <div className="w-20 sm:w-32 h-20 sm:h-32 rounded-full bg-customOrange p-1 shadow-lg">
                                                                    <div className="w-full h-full rounded-full bg-white p-1 sm:p-2">
                                                                        <motion.div
                                                                            className="w-full h-full rounded-full overflow-hidden bg-gray-100"
                                                                            animate={{ scale: [1, 1.05, 1] }}
                                                                            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                                                                        >
                                                                            {user.profilePhoto && user.profilePhoto.trim() !== "" ? (
                                                                                <Image
                                                                                    src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${user.profilePhoto}`}
                                                                                    alt={`${user.firstName || ""} ${user.lastName || ""}`}
                                                                                    width={isFirst ? 80 : 72}
                                                                                    height={isFirst ? 80 : 72}
                                                                                    className="w-full h-full object-cover"
                                                                                />
                                                                            ) : (
                                                                                <div className="w-full h-full flex items-center justify-center text-customOrange text-lg sm:text-2xl font-bold">
                                                                                    {(user.firstName?.charAt(0) || "") + (user.lastName?.charAt(0) || "")}
                                                                                </div>
                                                                            )}
                                                                        </motion.div>
                                                                    </div>
                                                                </div>
                                                                <div className={`absolute -bottom-2 left-1/2 transform -translate-x-1/2 ${badgeSize} rounded-full bg-customOrange flex items-center justify-center text-white font-bold ${badgeTextSize}`}>
                                                                    {user.rank}
                                                                </div>
                                                            </div>
                                                            <h3 className={nameClass}>
                                                                {user.studentName}
                                                            </h3>
                                                            <div className={scoreClass}>
                                                                <FaBolt className="mr-1 text-xs sm:text-sm" />
                                                                {user.score.toLocaleString()}
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        <>
                                                            <div className="relative mb-3 sm:mb-4">
                                                                <div className="rounded-full bg-customOrange p-1">
                                                                    <div className="rounded-full bg-white p-1">
                                                                        <motion.div
                                                                            className="rounded-full bg-white overflow-hidden relative"
                                                                            style={{ width: profileSize - 8, height: profileSize - 8 }}
                                                                        >
                                                                            {user.profilePhoto && user.profilePhoto.trim() !== "" ? (
                                                                                <Image
                                                                                    src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${user.profilePhoto}`}
                                                                                    alt={`${user.firstName || ""} ${user.lastName || ""}`}
                                                                                    width={profileSize - 8}
                                                                                    height={profileSize - 8}
                                                                                    className="w-full h-full object-cover"
                                                                                />
                                                                            ) : (
                                                                                <div className="w-full h-full flex items-center justify-center text-customOrange text-sm sm:text-lg font-bold">
                                                                                    {(user.firstName?.charAt(0) || "") + (user.lastName?.charAt(0) || "")}
                                                                                </div>
                                                                            )}
                                                                        </motion.div>
                                                                    </div>
                                                                </div>
                                                                <div className={`absolute -bottom-2 left-1/2 transform -translate-x-1/2 ${badgeSize} rounded-full ${isFirst ? 'bg-customOrange' : 'bg-orange-400'} flex items-center justify-center text-white font-bold ${badgeTextSize}`}>
                                                                    {user.rank}
                                                                </div>
                                                            </div>
                                                            <h3 className={nameClass}>{user.studentName}</h3>
                                                            <div className={scoreClass}>
                                                                <FaBolt className="mr-1 text-xs sm:text-sm" />
                                                                {user.score.toLocaleString()}
                                                            </div>
                                                        </>
                                                    )}
                                                </motion.div>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}
                            {others.length > 0 && (
                                <div className="rounded-xl p-4 sm:p-6">
                                    <div className="space-y-3">
                                        {others.map((user, index) => {
                                            const isLoggedInUser = user.studentId === loggedInUserId;

                                            return (
                                                <motion.div
                                                    key={user.studentId}
                                                    initial={{ x: -20, opacity: 0 }}
                                                    animate={{ x: 0, opacity: 1 }}
                                                    transition={{ delay: index * 0.05 }}
                                                    className={`flex items-center justify-between p-3 sm:p-4 rounded-xl border transition-all duration-200 relative overflow-hidden ${user.rank === 1
                                                        ? 'bg-gradient-to-r from-orange-50 to-orange-100 border-customOrange border-2 border-l-4 sm:border-l-5'
                                                        : 'bg-white border-gray-200 hover:bg-gray-50'
                                                        }`}
                                                >
                                                    <div className="flex items-center gap-2 sm:gap-4 flex-1">
                                                        <div className={`flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-full font-bold text-sm sm:text-lg relative ${user.rank === 1
                                                            ? 'bg-gradient-to-br from-customOrange to-orange-600 text-white'
                                                            : 'bg-customOrange/20 text-customOrange'
                                                            }`}>
                                                            {user.rank}
                                                        </div>
                                                        <div className="relative">
                                                            {renderProfile(user, 32, false)}
                                                        </div>
                                                        <div className="flex-1 min-w-0">
                                                            <p className={`font-bold text-sm sm:text-base truncate ${user.rank === 1 ? 'text-customOrange' : 'text-black'}`}>
                                                                {user.studentName}
                                                                {isLoggedInUser && (
                                                                    <span className="ml-1 sm:ml-2 text-xs text-customOrange px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full border border-orange-500 inline-block">
                                                                        You
                                                                    </span>
                                                                )}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-1 rounded-full font-bold border text-xs sm:text-sm ${user.rank === 1
                                                        ? 'text-customOrange border-customOrange border-2 bg-orange-100'
                                                        : 'text-customOrange border-orange-300 shadow-md shadow-gray-200'
                                                        }`}>
                                                        <FaBolt className="text-orange-500 text-xs sm:text-sm" />
                                                        <span>{user.score.toLocaleString()}</span>
                                                    </div>
                                                </motion.div>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}

                    {hasMoreRecords && !loading && (
                        <div className="flex justify-center mt-6">
                            <Button
                                onClick={loadMoreRecords}
                                disabled={loadingMore}
                                className="px-6 py-2 sm:px-8 sm:py-3 rounded-full bg-customOrange text-white hover:bg-orange-600 disabled:bg-gray-400 font-semibold min-w-[120px] sm:min-w-[140px] text-sm sm:text-base"
                            >
                                {loadingMore ? (
                                    <div className="flex items-center gap-2">
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                        Loading...
                                    </div>
                                ) : (
                                    "Load More"
                                )}
                            </Button>
                        </div>
                    )}

                    {!loading && !error && totalRecords > 0 && (
                        <div className="text-center text-gray-500 text-xs sm:text-sm mt-4 sm:mt-6">
                            Showing {leaderboard.length} of {totalRecords} participants
                        </div>
                    )}
                </div>
            </div>
            <Footer />
        </>
    );
}