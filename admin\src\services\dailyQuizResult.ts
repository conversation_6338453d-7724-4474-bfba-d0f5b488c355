import axiosInstance from "@/lib/axios";

export const getMockExamResults = async (
  studentId: string,
  page: number = 1,
  limit: number = 10
): Promise<any> => {
  try {
    const response = await axiosInstance.get(`/mock-exam-result/${studentId}?page=${page}&limit=${limit}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get mock exam result: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};