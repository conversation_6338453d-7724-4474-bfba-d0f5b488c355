import { Request, Response } from "express";
import {
  canSpinToday,
  deductCoinsFor<PERSON>pin,
  saveSpinReward,
  creditCoinsForSpin,
} from "../services/spinService";
import { UserType } from "@prisma/client";

export const checkSpinEligibilityController = async (req: Request, res: Response) => {
  const { modelId, modelType } = req.query;

  if (typeof modelId !== "string" || typeof modelType !== "string") {
    return res.status(400).json({ success: false, message: "Invalid params" });
  }

  if (!["STUDENT", "CLASS"].includes(modelType)) {
    return res.status(400).json({ success: false, message: "Invalid user type" });
  }

  try {
    const eligible = await canSpinToday(modelId, modelType as UserType);
    return res.json({ success: true, eligible });
  } catch (err: any) {
    console.error("Error checking spin eligibility:", err);
    return res.status(500).json({ success: false, message: "Internal error" });
  }
};

export const deductSpinCoinsController = async (req: Request, res: Response): Promise<any> => {
  const { modelId, modelType } = req.body;

  if (!modelId || !modelType) {
    return res.status(400).json({ success: false, message: "modelId and modelType are required" });
  }

  try {
    const result = await deductCoinsForSpin(modelId, modelType);
    return res.status(result.success ? 200 : 400).json(result);
  } catch (err: any) {
    console.error("Error deducting coins for spin:", err);
    return res.status(500).json({ success: false, message: "Internal error", error: err.message });
  }
};

export const saveSpinRewardController = async (req: Request, res: Response): Promise<any> => {
  const { modelId, modelType, rewardId } = req.body;

  if (!modelId || !modelType || !rewardId) {
    return res.status(400).json({ success: false, message: "modelId, modelType, and rewardId are required" });
  }

  try {
    const result = await saveSpinReward(modelId, modelType, rewardId);
    return res.status(result.success ? 200 : 500).json(result);
  } catch (err: any) {
    console.error("Error saving spin reward:", err);
    return res.status(500).json({ success: false, message: "Internal error", error: err.message });
  }
};

export const creditCoinsController = async (req: Request, res: Response): Promise<any> => {
  const { modelId, modelType, coins } = req.body;

  if (!modelId || !modelType || !coins) {
    return res.status(400).json({ success: false, message: "modelId, modelType, and coins are required" });
  }

  try {
    const result = await creditCoinsForSpin(modelId, modelType, coins);
    return res.status(result.success ? 200 : 500).json(result);
  } catch (err: any) {
    console.error("Error crediting coins for spin:", err);
    return res.status(500).json({ success: false, message: "Internal error", error: err.message });
  }
};