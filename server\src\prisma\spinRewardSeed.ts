import { PrismaClient } from '@prisma/client';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config({ path: path.resolve(__dirname, '../../.env') });

const prisma = new PrismaClient();

const prizes = [
  { rewardName: 'Laptop', rewardType: 'item' },
  { rewardName: 'Keychain', rewardType: 'item' },
  { rewardName: '3 coins', rewardType: 'coins' },
  { rewardName: 'uest super card', rewardType: 'item' },
  { rewardName: 'Better luck next time', rewardType: 'nothing' },
  { rewardName: '1 notebook', rewardType: 'item' },
];

async function seedSpinRewards() {
  try {
    console.log('Seeding SpinRewards...');

    await prisma.spinRewards.deleteMany({});
    console.log('Cleared existing SpinRewards');

    for (const prize of prizes) {
      await prisma.spinRewards.create({
        data: {
          rewardName: prize.rewardName,
          rewardType: prize.rewardType,
          createdAt: new Date(),
        },
      });
      console.log(`Created: ${prize.rewardName}`);
    }

    console.log('<PERSON>R<PERSON><PERSON> seeded successfully!');
  } catch (error) {
    console.error('Error during seed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedSpinRewards();