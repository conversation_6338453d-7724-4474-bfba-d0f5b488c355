import { PrismaClient } from '@prisma/client';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config({ path: path.resolve(__dirname, '../../.env') });

const prisma = new PrismaClient();

const prizes = [
  { id: '2ee90e37-b176-45ba-991f-7e18d3d0191b', rewardName: 'Laptop', rewardType: 'item' },
  { id: '685e2c00-d8ad-47bf-92f2-3c68fd189f08', rewardName: 'Keychain', rewardType: 'item' },
  { id: '7d52f598-5a08-41d3-8e8d-c1fd0c821e29', rewardName: '3 coins', rewardType: 'coins' },
  { id: '986b9573-9875-4ef8-9f6e-ec5e5f92d014', rewardName: 'uest super card', rewardType: 'item' },
  { id: 'e5328123-d443-4285-b600-9b44d55b7b06', rewardName: 'Better luck next time', rewardType: 'nothing' },
  { id: '8d40c969-3094-48af-a99a-a960faa1a380', rewardName: '1 notebook', rewardType: 'item' },
];

async function seedSpinRewards() {
  try {
    console.log('Seeding SpinRewards...');

    await prisma.spinRewards.deleteMany({});
    console.log('Cleared existing SpinRewards');

    for (const prize of prizes) {
      await prisma.spinRewards.create({
        data: {
          id: prize.id,
          rewardName: prize.rewardName,
          rewardType: prize.rewardType,
          createdAt: new Date(),
        },
      });
      console.log(`Created: ${prize.rewardName} (${prize.id})`);
    }

    console.log('SpinRewards seeded successfully!');
  } catch (error) {
    console.error('Error during seed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedSpinRewards();
