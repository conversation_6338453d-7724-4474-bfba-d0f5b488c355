import prisma from "@/config/prismaClient";
import { UserType, NotificationType } from "@prisma/client";
import { createNotification } from "@/utils/notifications";
import { logUserActivity } from "@/utils/activityLogger";

export const canSpinToday = async (
  modelId: string,
  modelType: UserType | "CLASS" | "STUDENT"
): Promise<boolean> => {
  const now = new Date();
  const todayStart = new Date(now);
  todayStart.setHours(0, 0, 0, 0);

  const spin = await prisma.spinRewardWinner.findFirst({
    where: {
      modelId,
      modelType,
      createdAt: {
        gte: todayStart,
      },
    },
  });

  const canSpin = !spin;

  // Log eligibility check
  await logUserActivity({
    userId: modelId,
    userType: modelType === "STUDENT" ? UserType.STUDENT : UserType.CLASS,
    activityType: canSpin ? "SPIN_ELIGIBLE_CHECK" : "SPIN_INELIGIBLE_CHECK",
  });

  return canSpin;
};

export const deductCoinsForSpin = async (
  modelId: string,
  modelType: UserType | "CLASS" | "STUDENT"
) => {
  try {
    const coinsEntry = await prisma.uestCoins.findUnique({
      where: {
        modelId_modelType: {
          modelId,
          modelType,
        },
      },
    });

    const currentCoins = coinsEntry?.coins ?? 0;

    if (currentCoins < 10) {
      // Log failed spin attempt
      await logUserActivity({
        userId: modelId,
        userType: modelType === "STUDENT" ? UserType.STUDENT : UserType.CLASS,
        activityType: "SPIN_FAILED_INSUFFICIENT_COINS",
      });
      return { success: false, message: "Not enough coins" };
    }

    const updated = await prisma.uestCoins.update({
      where: {
        modelId_modelType: {
          modelId,
          modelType,
        },
      },
      data: {
        coins: {
          decrement: 10,
        },
      },
    });

    await prisma.uestCoinTransaction.create({
      data: {
        modelId,
        modelType,
        type: "DEBIT",
        amount: 10,
        reason: "Spin again",
      },
    });

    // Log paid spin
    await logUserActivity({
      userId: modelId,
      userType: modelType === "STUDENT" ? UserType.STUDENT : UserType.CLASS,
      activityType: "SPIN_PAID",
    });

    await createNotification({
      userId: modelId,
      userType: modelType === "STUDENT" ? UserType.STUDENT : UserType.CLASS,
      type: NotificationType.SPIN_COIN_DEBIT,
      title: "Spin Deducted 10 Coins",
      message: `You used 10 coins to spin the wheel. Remaining balance: ${updated.coins} coins.`,
      data: {
        amount: 10,
        reason: "Spin again",
        balance: updated.coins,
      },
    });

    return { success: true, message: "10 coins deducted for spin" };
  } catch (err: any) {
    return { success: false, message: "Internal error", error: err?.message };
  }
};

export const saveSpinReward = async (
  modelId: string,
  modelType: UserType | "CLASS" | "STUDENT",
  rewardId: string
) => {
  try {
    const userTypeEnum = modelType === "STUDENT" ? UserType.STUDENT :
                        modelType === "CLASS" ? UserType.CLASS :
                        modelType as UserType;

    // Get reward details for logging
    const rewardDetails = await prisma.spinRewards.findUnique({
      where: { id: rewardId }
    });

    const entry = await prisma.spinRewardWinner.create({
      data: {
        modelId,
        modelType: userTypeEnum,
        rewardId,
      },
    });

    // Log spin activity with reward details
    await logUserActivity({
      userId: modelId,
      userType: userTypeEnum,
      activityType: `SPIN_WON_${rewardDetails?.rewardName?.toUpperCase().replace(/\s+/g, '_') || 'UNKNOWN'}`,
    });

    // Also log general spin activity
    await logUserActivity({
      userId: modelId,
      userType: userTypeEnum,
      activityType: "SPIN_COMPLETED",
    });

    return { success: true, message: "Reward saved", data: entry };
  } catch (err: any) {
    return { success: false, message: "Could not save reward", error: err?.message };
  }
};

export const creditCoinsForSpin = async (
  modelId: string,
  modelType: UserType | "CLASS" | "STUDENT",
  coins: number
) => {
  try {
    const updated = await prisma.uestCoins.upsert({
      where: {
        modelId_modelType: {
          modelId,
          modelType,
        },
      },
      create: {
        modelId,
        modelType,
        coins,
      },
      update: {
        coins: {
          increment: coins,
        },
      },
    });

    await prisma.uestCoinTransaction.create({
      data: {
        modelId,
        modelType,
        type: "CREDIT",
        amount: coins,
        reason: "Spin reward",
      },
    });

    // Log coin credit activity
    await logUserActivity({
      userId: modelId,
      userType: modelType === "STUDENT" ? UserType.STUDENT : UserType.CLASS,
      activityType: `SPIN_COINS_CREDITED_${coins}`,
    });

    await createNotification({
      userId: modelId,
      userType: modelType === "STUDENT" ? UserType.STUDENT : UserType.CLASS,
      type: NotificationType.SPIN_COIN_CREDIT,
      title: `You won ${coins} coins! 🎉`,
      message: `Congratulations! You won ${coins} coins from the spin. New balance: ${updated.coins} coins.`,
      data: {
        amount: coins,
        reason: "Spin reward",
        balance: updated.coins,
      },
    });

    return { success: true, message: `${coins} coins credited as spin reward` };
  } catch (err: any) {
    return { success: false, message: "Internal error", error: err?.message };
  }
};