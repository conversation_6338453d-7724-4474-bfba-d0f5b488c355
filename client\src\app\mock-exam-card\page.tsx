"use client";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { getMockExamTopThreeStudents } from "@/services/LeaderboardUserApi";
import MockExamButton from "../mock-test/mockExamButton";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import SpinningWheel from "@/components/SpinningWheel";

// import { BookCheck } from "lucide-react";
// import WeeklyExamButton from "../mock-test/weeklyExam";

const Page = () => {
    const router = useRouter();

    let studentId: string | null = null;
    try {
        const data = localStorage.getItem("student_data");
        studentId = data ? JSON.parse(data).id : null;
    } catch {
        studentId = null;
    }

    const [top3, setTop3] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        const fetchTop3 = async () => {
            try {
                setLoading(true);
                const response = await getMockExamTopThreeStudents();
                if (response.success) setTop3(response.data.data);
                else setError(response.error);
            } catch (err: any) {
                setError(`Failed to load top performers ${err.message}`);
            } finally {
                setLoading(false);
            }
        };
        fetchTop3();
    }, []);

    return (
        <div className="min-h-screen bg-black text-white">
            <Header />

            <div className="fixed right-0 top-10 z-50">
                <div
                    onClick={() => setIsOpen(true)}
                    className="shadow-lg cursor-pointer transform -rotate-90 origin-bottom-right bg-[#FD904B] text-white px-4 py-2 rounded-t-lg"
                >
                    🎯 Spin the Wheel
                </div>

                {isOpen && (
                    <div className="fixed inset-0 bg-black/30 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-y-auto">
                        <div className="relative w-full max-w-4xl mx-auto">
                            <div className="bg-white dark:bg-gray-100 rounded-xl shadow-xl overflow-hidden">
                                <SpinningWheel isOpen={isOpen} setIsOpen={setIsOpen} />
                            </div>
                        </div>
                    </div>
                )}
            </div>

            <section className="relative py-10 sm:py-16 px-3 sm:px-8">
                <div className="max-w-6xl mx-auto relative z-10">
                    <div className="text-center mb-10 sm:mb-12">
                        <h2 className="text-2xl sm:text-4xl md:text-5xl font-extrabold tracking-wide mb-3">
                            🎉 Yesterday&apos;s Top Performers
                        </h2>
                        <p className="text-gray-400 text-sm sm:text-base md:text-lg max-w-2xl mx-auto">
                            The elite students who crushed the challenge and topped the leaderboard!
                        </p>
                    </div>

                    {loading && <p className="text-center text-gray-500">Loading top performers...</p>}
                    {error && <p className="text-center text-red-500">{error}</p>}

                    <div className="relative h-auto sm:h-[340px] flex flex-wrap sm:flex-nowrap items-end justify-center gap-4 sm:gap-6">
                        {[1, 0, 2].map((rankIndex, i) => {
                            const student = top3[rankIndex];
                            if (!student) return null;

                            const heights = ["h-3/4", "h-5/6", "h-2/3"];
                            const medals = ["🥈", "🥇", "🥉"];
                            const accentGlow = i === 1 ? "shadow-[0_0_20px_#FD904B]" : "shadow-lg";

                            return (
                                <motion.div
                                    key={student.rank}
                                    initial={{ opacity: 0, y: 40 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.4, delay: i * 0.2 }}
                                    className={`w-full ${heights[i]} bg-neutral-900 p-10 ${accentGlow} rounded-t-2xl flex flex-col items-center justify-end pb-6 relative border border-neutral-700 hover:scale-105 transition-all`}
                                >
                                    <div className="absolute -top-5 text-3xl sm:text-4xl z-10">{medals[i]}</div>
                                    <div className="mb-4">
                                        <div className="relative w-20 h-20 sm:w-24 sm:h-24 rounded-full overflow-hidden border-4 border-white shadow-lg">
                                            {student.profilePhoto ? (
                                                <Image
                                                    src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${student.profilePhoto.replace(/\\/g, "/")}`}
                                                    alt={student.firstName}
                                                    width={96}
                                                    height={96}
                                                    className="w-full h-full object-cover"
                                                />
                                            ) : (
                                                <div className="w-full h-full bg-gray-700 flex items-center justify-center text-lg sm:text-xl font-bold text-white">
                                                    {student.firstName?.[0] || "U"}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                    <div className="text-center px-2 sm:px-4">
                                        <h3 className="font-bold text-white text-xs sm:text-base truncate">
                                            {student.firstName} {student.lastName}
                                        </h3>
                                        <div className="flex justify-center gap-1 mt-2 flex-wrap">
                                            <span className="bg-[#FD904B]/20 text-white text-xs px-2 py-1 rounded-full">
                                                ⚡ {student.score}
                                            </span>
                                            <span className="bg-[#FD904B]/20 text-white text-xs px-2 py-1 rounded-full">
                                                🔥 {student.streakCount}
                                            </span>
                                        </div>
                                    </div>
                                </motion.div>
                            );
                        })}
                    </div>
                    <div className="flex justify-center mb-12 mt-12">
                        <MockExamButton />
                    </div>

                    <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4">
                        <button
                            onClick={() => router.push("/Leader-Board")}
                            className="bg-black hover:bg-neutral-800 border-2 border-[#FD904B] hover:border-[#E88143] text-white px-5 py-2 sm:px-6 sm:py-3 rounded-lg font-bold text-sm sm:text-base transition-all"
                        >
                            🏆 Leaderboard
                        </button>
                        {
                            studentId && (
                                <button
                                    onClick={() => router.push(`/mock-exam-result/${studentId}`)}
                                    className="bg-black hover:bg-neutral-800 border-2 border-[#FD904B] hover:border-[#E88143] text-white px-5 py-2 sm:px-6 sm:py-3 rounded-lg font-bold text-sm sm:text-base transition-all"
                                >
                                    📊 My Results
                                </button>
                            )
                        }

                    </div>
                </div>
            </section>

            <section className="relative py-12 px-3 sm:px-6 bg-white text-black rounded-t-3xl">
                <div className="max-w-4xl mx-auto relative">
                    <div className="text-center mb-1">
                        <span className="text-xs font-bold tracking-widest text-[#FD904B] uppercase">
                            Knowledge Challenge
                        </span>
                    </div>

                    <h2 className="text-2xl sm:text-4xl font-extrabold text-center mb-6">
                        Daily Quiz
                    </h2>

                    <p className="text-sm sm:text-lg text-center max-w-2xl mx-auto mb-10 leading-relaxed text-gray-700">
                        Test your skills in our fast-paced daily challenge. Earn rewards,
                        build streaks, and dominate the leaderboards!
                    </p>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.4 }}
                        className="mt-4 mb-8 mx-auto w-full max-w-3xl bg-neutral-900 border border-neutral-700 rounded-xl p-4 sm:p-6 shadow-lg flex flex-col sm:flex-row items-center justify-between gap-4"
                    >
                        <div className="flex items-center gap-3 text-center sm:text-left">
                            <span className="text-2xl">🛍️</span>
                            <p className="text-sm sm:text-lg font-semibold text-white">
                                The <span className="text-[#FD904B]">Store</span> is now <strong>LIVE!</strong> Redeem your coins for exciting rewards!
                            </p>
                        </div>
                        <Button
                            className="bg-[#FD904B] hover:bg-orange-600 text-white font-semibold rounded-lg px-4 py-2 sm:px-6 sm:py-2.5 transition-all"
                            onClick={() => router.push("/store")}
                        >
                            Visit Store
                        </Button>
                    </motion.div>

                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-12">
                        {[
                            { icon: "📝", text: "10 Questions" },
                            { icon: "⏱️", text: "8 Minutes" },
                            { icon: "💰", text: "25 Coins" },
                            { icon: "🔥", text: "Medium Level" },
                        ].map((item, i) => (
                            <div
                                key={i}
                                className="bg-white border border-neutral-200 rounded-xl p-5 text-center shadow hover:scale-105 transition-all min-w-[130px]"
                            >
                                <div className="text-2xl sm:text-3xl mb-3">{item.icon}</div>
                                <div className="font-bold text-sm sm:text-base">{item.text}</div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            <section className="relative py-10 px-3 sm:px-6 bg-neutral-900 text-white">
                <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
                    <div className="bg-neutral-800 p-6 sm:p-8 rounded-2xl shadow-xl border border-neutral-700">
                        <h3 className="text-2xl sm:text-3xl font-bold text-[#FD904B] mb-6 flex items-center">
                            <span className="mr-3">🧾</span> Rules & Rewards
                        </h3>
                        <ul className="space-y-3 sm:space-y-4 text-gray-200">
                            {[
                                { icon: "🕒", text: "1 attempt per day" },
                                { icon: "💰", text: "Coins based on score" },
                                { icon: "🔥", text: "Daily streaks = bonus coins" },
                                { icon: "🏆", text: "Top 3 win stationery prizes" },
                            ].map((item, i) => (
                                <li key={i} className="flex items-start text-sm sm:text-lg">
                                    <span className="text-xl sm:text-2xl mr-3">{item.icon}</span>
                                    <span>{item.text}</span>
                                </li>
                            ))}
                        </ul>

                        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 mt-6 sm:mt-8">
                            {[
                                "50% : 0 coins",
                                "60% : 1 coin",
                                "70% : 2 coins",
                                "80% : 3 coins",
                                "90% : 4 coins",
                                "100% : 5 coins",
                            ].map((reward, i) => (
                                <div
                                    key={i}
                                    className="bg-neutral-900 border text-white border-[#FD904B] rounded-lg p-2 sm:p-3 text-xs sm:text-sm font-bold text-center shadow-sm"
                                >
                                    {reward}
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="bg-neutral-800 p-6 sm:p-8 rounded-2xl shadow-xl border border-neutral-700">
                        <h3 className="text-2xl sm:text-3xl font-bold text-[#FD904B] mb-8 flex items-center">
                            <span className="mr-3">🏅</span> Achievement Badges
                        </h3>
                        <div className="grid grid-cols-2 sm:grid-cols-3 gap-6">
                            {[
                                { label: "100 coins", badge: "/scholer.svg" },
                                { label: "500 coins", badge: "/Mastermind.svg" },
                                { label: "1000 coins", badge: "/Achiever.svg" },
                                { label: "30 days", badge: "/Perfect Month.svg" },
                                { label: "365 days", badge: "/Perfect Year.svg" },
                                { label: "Daily streak", badge: "/Streak.svg" },
                            ].map((item, i) => (
                                <div key={i} className="text-center">
                                    <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto bg-neutral-800 rounded-full shadow-lg mb-3 overflow-hidden">
                                        <Image
                                            src={item.badge}
                                            alt={item.label}
                                            width={80}
                                            height={80}
                                            className="object-cover w-full h-full"
                                        />
                                    </div>
                                    <p className="text-xs sm:text-sm font-medium">{item.label}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            <section className="relative py-12 px-3 sm:px-6 bg-white text-black">
                <div className="max-w-4xl mx-auto">
                    <h3 className="text-2xl sm:text-3xl font-bold text-[#FD904B] mb-8 sm:mb-10 text-center">
                        🔥 Streak Bonuses
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
                        {[
                            { icon: "🔥", title: "Daily Streak", reward: "+1 coin/day", note: "Maintain your streak!" },
                            { icon: "🏆", title: "Weekly Challenge", reward: "+5 coins", note: "Complete Sunday Challenge!" },
                            { icon: "💯", title: "Perfect Score", reward: "+3 coins", note: "Get all answers right!" },
                        ].map((item, i) => (
                            <div key={i} className="bg-white border border-neutral-200 p-4 sm:p-6 rounded-xl shadow hover:scale-105 transition-all text-center">
                                <div className="text-[#FD904B] text-3xl sm:text-4xl mb-3 sm:mb-4">{item.icon}</div>
                                <h4 className="text-lg sm:text-xl font-bold mb-1">{item.title}</h4>
                                <p className="text-gray-700 text-sm sm:text-base">{item.reward}</p>
                                <div className="mt-3 sm:mt-4 bg-[#FD904B]/20 text-[#FD904B] text-xs sm:text-sm font-medium px-3 py-1 rounded-full inline-block">
                                    {item.note}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            <Footer />
        </div>
    );
};

export default Page;