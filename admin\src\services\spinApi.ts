import { axiosInstance } from '@/lib/axios';

export interface SpinWinner {
  id: string;
  modelId: string;
  modelType: 'STUDENT' | 'CLASS';
  name: string;
  email: string;
  profilePhoto: string | null;
  rewardName: string;
  rewardType: string;
  rewardImage: string | null;
  rewardId: string;
  wonAt: string;
  userDetails?: any;
}

export interface SpinWinnersResponse {
  data: SpinWinner[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    limit: number;
  };
}

export interface SpinWinnersFilters {
  page?: number;
  limit?: number;
  search?: string;
  modelType?: string;
  rewardName?: string;
}

export const getSpinWinners = async (filters: SpinWinnersFilters = {}): Promise<SpinWinnersResponse> => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      modelType,
      rewardName,
    } = filters;

    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (search) params.append('search', search);
    if (modelType && modelType !== 'ALL') params.append('modelType', modelType);
    if (rewardName && rewardName !== 'ALL') params.append('rewardName', rewardName);

    const response = await axiosInstance.get(`/admin/store/orders/spin-winners?${params}`);
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to fetch spin winners');
    }

    return {
      data: response.data.data,
      pagination: response.data.pagination,
    };
  } catch (error: any) {
    console.error('Error fetching spin winners:', error);
    throw new Error(error.response?.data?.message || error.message || 'Failed to fetch spin winners');
  }
};
