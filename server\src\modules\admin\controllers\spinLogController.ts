import { Request, Response } from 'express';
import { sendError, sendSuccess } from '@/utils/response';
import { getSpinLogs, getSpinStats } from '../services/spinLogService';

export const getAllSpinLogs = async (req: Request, res: Response): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const userType = req.query.userType as string;
    const search = req.query.search as string;
    const activityType = req.query.activityType as string;

    const result = await getSpinLogs({
      page,
      limit,
      userType,
      search,
      activityType,
    });

    return sendSuccess(res, result, 'Spin logs retrieved successfully');
  } catch (error: any) {
    console.error('Get spin logs error:', error);
    return sendError(res, `Failed to retrieve spin logs: ${error.message}`, 500);
  }
};

export const getSpinStatsController = async (req: Request, res: Response): Promise<any> => {
  try {
    const stats = await getSpinStats();
    return sendSuccess(res, stats, 'Spin statistics retrieved successfully');
  } catch (error: any) {
    console.error('Get spin stats error:', error);
    return sendError(res, `Failed to retrieve spin statistics: ${error.message}`, 500);
  }
};
