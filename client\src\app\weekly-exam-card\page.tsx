"use client";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { motion } from "framer-motion";
import WeeklyExamButton from "../mock-test/weeklyExam";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import SpinningWheel from "@/components/SpinningWheel";
import { useState } from "react";

const WeeklyExamPage = () => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="min-h-screen flex flex-col bg-black text-white">
      <Header />

      <div className="fixed right-0 top-10 z-50">
        <div
          onClick={() => setIsOpen(true)}
          className="shadow-lg cursor-pointer transform -rotate-90 origin-bottom-right bg-[#FD904B] text-white px-4 py-2 rounded-t-lg"
        >
          🎯 Spin the Wheel
        </div>

        {isOpen && (
          <div className="fixed inset-0 bg-black/30 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-y-auto">
            <div className="relative w-full max-w-4xl mx-auto">
              <div className="bg-white dark:bg-gray-100 rounded-xl shadow-xl overflow-hidden">
                <SpinningWheel isOpen={isOpen} setIsOpen={setIsOpen} />
              </div>
            </div>
          </div>
        )}
      </div>

      <section className="relative py-16 px-4 sm:px-8 overflow-hidden">
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1/4 left-1/2 -translate-x-1/2 w-[500px] h-[500px]rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-6xl mx-auto text-center z-10">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-4xl sm:text-6xl font-extrabold mb-6 leading-tight"
          >
            🏆 Weekly <span className="text-[#FD904B]">Quiz Challenge</span>
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-gray-300 max-w-2xl mx-auto mb-10 text-lg sm:text-xl"
          >
            Compete every Sunday with <span className="text-white font-semibold">25 exciting questions</span>. Rise to the top, earn rewards, and build your winning streak.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4, delay: 0.2 }}
          >
            <WeeklyExamButton />
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="relative z-10 mt-12 mx-auto w-full max-w-3xl bg-neutral-800/70 backdrop-blur-sm border border-[#FD904B]/40 rounded-2xl p-6 shadow-lg flex flex-col sm:flex-row items-center justify-between gap-4"
        >
          <div className="flex items-center gap-3 text-center sm:text-left">
            <span className="text-3xl">🛍️</span>
            <p className="text-base sm:text-lg font-semibold text-white">
              The <span className="text-[#FD904B]">Store</span> is now{" "}
              <strong>LIVE!</strong> Redeem your coins for exciting rewards!
            </p>
          </div>
          <Button
            className="bg-[#FD904B] hover:bg-orange-600 text-white font-semibold rounded-lg px-6 py-2 sm:py-2.5 transition-all"
            onClick={() => router.push("/store")}
          >
            Visit Store
          </Button>
        </motion.div>
      </section>

      <section className="bg-white text-black py-16 px-4 sm:px-8 flex-grow">
        <div className="max-w-5xl mx-auto grid grid-cols-2 sm:grid-cols-4 gap-6">
          {[
            { icon: "📝", text: "25 Questions" },
            { icon: "⏱️", text: "15 Minutes" },
            { icon: "💰", text: "Free Entry" },
            { icon: "🔥", text: "Earn Coins" },
          ].map((item, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: i * 0.1 }}
              whileHover={{ scale: 1.05 }}
              className="bg-white border border-neutral-200 rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-all"
            >
              <div className="text-3xl mb-3">{item.icon}</div>
              <div className="font-bold text-base sm:text-lg">{item.text}</div>
            </motion.div>
          ))}
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default WeeklyExamPage;