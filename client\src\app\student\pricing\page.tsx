"use client";
import Footer from "@/app-components/Footer";
import Header from "@/app-components/Header";
import { useState } from "react";

export default function PricingPage() {
  const [billing, setBilling] = useState<"monthly" | "yearly">("monthly");

  const plans = [
    {
      name: "Pro (Coming Soon)",
      description: "Best for students who need more learning.",
      monthlyPrice: 18,
      yearlyPrice: 180,
      features: [
        "Free Uest Merchandise Gift",
        "Priority support",
        "Multiplayer Games",
        "Access to exclusive content",
        "Free Weekly Quiz Participation",
        "Free Quiz Learning Resources",
        "Free Classes Demo",
        "Free Uwhiz Question Bank",
      ],
    },
  ];

  return (
    <>
      <Header />
      <div className="min-h-screen bg-black text-white flex flex-col items-center py-16 px-4">
        <h1 className="text-4xl font-bold mb-4">Student Pricing Plans</h1>
        <p className="text-gray-300 mb-10 text-center max-w-lg">
          Choose the plan that fits your needs. Switch between monthly and
          yearly billing anytime.
        </p>

        {/* Toggle */}
        <div className="flex items-center gap-4 mb-12">
          <span
            className={
              billing === "monthly" ? "text-[#FD904B]" : "text-gray-400"
            }
          >
            Monthly
          </span>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={billing === "yearly"}
              onChange={() =>
                setBilling(billing === "monthly" ? "yearly" : "monthly")
              }
            />
            <div className="w-14 h-7 bg-gray-600 rounded-full peer peer-checked:bg-[#FD904B] transition-colors"></div>
            <span className="absolute left-1 top-1 w-5 h-5 bg-white rounded-full peer-checked:translate-x-7 transform transition-transform"></span>
          </label>
          <span
            className={
              billing === "yearly" ? "text-[#FD904B]" : "text-gray-400"
            }
          >
            Yearly
          </span>
        </div>

        {/* Single Plan - Centered */}
        <div className="flex justify-center w-full">
          <div className="bg-white text-black rounded-xl shadow-lg p-8 flex flex-col w-full max-w-md">
            <h2 className="text-2xl font-bold mb-2">{plans[0].name}</h2>
            <p className="text-gray-600 mb-6">{plans[0].description}</p>
            <div className="text-4xl font-bold mb-4 text-[#FD904B]">
              ₹
              {billing === "monthly"
                ? plans[0].monthlyPrice
                : plans[0].yearlyPrice}
              <span className="text-base text-gray-500 font-normal">
                /{billing === "monthly" ? "month" : "year"}
              </span>
            </div>
            <ul className="mb-6 space-y-2 text-gray-700">
              {plans[0].features.map((feature, idx) => (
                <li key={idx} className="flex items-center">
                  <span className="w-2 h-2 bg-[#FD904B] rounded-full mr-3"></span>
                  {feature}
                </li>
              ))}
            </ul>
            <button className="mt-auto bg-[#FD904B] text-white py-3 px-5 rounded-lg font-semibold hover:opacity-90 transition">
              Get Started
            </button>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}