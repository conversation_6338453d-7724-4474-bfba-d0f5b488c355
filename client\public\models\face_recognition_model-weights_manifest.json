[{"weights": [{"name": "conv32_down/conv/filters", "shape": [7, 7, 3, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0005260649557207145, "min": -0.07101876902229645}}, {"name": "conv32_down/conv/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 8.471445956577858e-07, "min": -0.00014740315964445472}}, {"name": "conv32_down/scale/weights", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.06814416062598135, "min": 5.788674831390381}}, {"name": "conv32_down/scale/biases", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.008471635042452345, "min": -0.931879854669758}}, {"name": "conv32_1/conv1/conv/filters", "shape": [3, 3, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0007328585666768691, "min": -0.0974701893680236}}, {"name": "conv32_1/conv1/conv/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 1.5952091238361e-08, "min": -1.978059313556764e-06}}, {"name": "conv32_1/conv1/scale/weights", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.02146628510718252, "min": 3.1103382110595703}}, {"name": "conv32_1/conv1/scale/biases", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0194976619645661, "min": -2.3787147596770644}}, {"name": "conv32_1/conv2/conv/filters", "shape": [3, 3, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0004114975824075587, "min": -0.05267169054816751}}, {"name": "conv32_1/conv2/conv/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 4.600177166424806e-09, "min": -5.70421968636676e-07}}, {"name": "conv32_1/conv2/scale/weights", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.03400764932819441, "min": 2.1677730083465576}}, {"name": "conv32_1/conv2/scale/biases", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010974494616190593, "min": -1.240117891629537}}, {"name": "conv32_2/conv1/conv/filters", "shape": [3, 3, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0005358753251094444, "min": -0.0760942961655411}}, {"name": "conv32_2/conv1/conv/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 5.9886454383719385e-09, "min": -7.366033889197485e-07}}, {"name": "conv32_2/conv1/scale/weights", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.014633869657329485, "min": 2.769575357437134}}, {"name": "conv32_2/conv1/scale/biases", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.022131107367721257, "min": -2.5229462399202234}}, {"name": "conv32_2/conv2/conv/filters", "shape": [3, 3, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00030145110452876373, "min": -0.03949009469326805}}, {"name": "conv32_2/conv2/conv/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 6.8779549306497095e-09, "min": -9.010120959151119e-07}}, {"name": "conv32_2/conv2/scale/weights", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.03929369870354148, "min": 4.8010945320129395}}, {"name": "conv32_2/conv2/scale/biases", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010553357180427103, "min": -1.2452961472903983}}, {"name": "conv32_3/conv1/conv/filters", "shape": [3, 3, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0003133527642371608, "min": -0.040735859350830905}}, {"name": "conv32_3/conv1/conv/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 4.1064200719547974e-09, "min": -3.0387508532465503e-07}}, {"name": "conv32_3/conv1/scale/weights", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.009252088210161994, "min": 2.333256721496582}}, {"name": "conv32_3/conv1/scale/biases", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.007104101251153385, "min": -0.34810096130651585}}, {"name": "conv32_3/conv2/conv/filters", "shape": [3, 3, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00029995629892629733, "min": -0.031195455088334923}}, {"name": "conv32_3/conv2/conv/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 5.62726418316814e-09, "min": -6.921534945296811e-07}}, {"name": "conv32_3/conv2/scale/weights", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0467432975769043, "min": 5.362040996551514}}, {"name": "conv32_3/conv2/scale/biases", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010314425300149357, "min": -1.268674311918371}}, {"name": "conv64_down/conv1/conv/filters", "shape": [3, 3, 32, 64], "dtype": "float32"}, {"name": "conv64_down/conv1/conv/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 8.373908033218849e-10, "min": -1.172347124650639e-07}}, {"name": "conv64_down/conv1/scale/weights", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0066875364266189875, "min": 2.5088400840759277}}, {"name": "conv64_down/conv1/scale/biases", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01691421620986041, "min": -2.0973628100226906}}, {"name": "conv64_down/conv2/conv/filters", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "conv64_down/conv2/conv/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 2.3252014483766877e-09, "min": -2.673981665633191e-07}}, {"name": "conv64_down/conv2/scale/weights", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.032557439804077146, "min": 2.6351239681243896}}, {"name": "conv64_down/conv2/scale/biases", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.015429047509735706, "min": -1.5429047509735707}}, {"name": "conv64_1/conv1/conv/filters", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "conv64_1/conv1/conv/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 1.1319172039756998e-09, "min": -1.4941307092479238e-07}}, {"name": "conv64_1/conv1/scale/weights", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.007802607031429515, "min": 3.401733160018921}}, {"name": "conv64_1/conv1/scale/biases", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01425027146058924, "min": -0.6982633015688727}}, {"name": "conv64_1/conv2/conv/filters", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "conv64_1/conv2/conv/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 2.5635019893325435e-09, "min": -2.717312108692496e-07}}, {"name": "conv64_1/conv2/scale/weights", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.04062801716374416, "min": 3.542381525039673}}, {"name": "conv64_1/conv2/scale/biases", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.007973166306813557, "min": -0.7415044665336609}}, {"name": "conv64_2/conv1/conv/filters", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "conv64_2/conv1/conv/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 1.2535732661062331e-09, "min": -1.8302169685151004e-07}}, {"name": "conv64_2/conv1/scale/weights", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005631206549850164, "min": 2.9051668643951416}}, {"name": "conv64_2/conv1/scale/biases", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01859012585060269, "min": -2.3795361088771445}}, {"name": "conv64_2/conv2/conv/filters", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "conv64_2/conv2/conv/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 2.486726369919351e-09, "min": -3.5311514452854786e-07}}, {"name": "conv64_2/conv2/scale/weights", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.03740917467603497, "min": 5.571568965911865}}, {"name": "conv64_2/conv2/scale/biases", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006418555858088475, "min": -0.5263215803632549}}, {"name": "conv64_3/conv1/conv/filters", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "conv64_3/conv1/conv/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 7.432564576875473e-10, "min": -8.47312361763804e-08}}, {"name": "conv64_3/conv1/scale/weights", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006400122362024644, "min": 2.268010377883911}}, {"name": "conv64_3/conv1/scale/biases", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010945847922680425, "min": -1.3353934465670119}}, {"name": "conv64_3/conv2/conv/filters", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "conv64_3/conv2/conv/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 2.278228722014533e-09, "min": -3.212302498040492e-07}}, {"name": "conv64_3/conv2/scale/weights", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.029840927498013366, "min": 7.038398265838623}}, {"name": "conv64_3/conv2/scale/biases", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010651412197187834, "min": -1.161003929493474}}, {"name": "conv128_down/conv1/conv/filters", "shape": [3, 3, 64, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00020040544662989823, "min": -0.022245004575918704}}, {"name": "conv128_down/conv1/conv/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 4.3550543563576545e-10, "min": -4.311503812794078e-08}}, {"name": "conv128_down/conv1/scale/weights", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.007448580685783835, "min": 2.830846071243286}}, {"name": "conv128_down/conv1/scale/biases", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01211262824488621, "min": -1.6957679542840696}}, {"name": "conv128_down/conv2/conv/filters", "shape": [3, 3, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00022380277514457702, "min": -0.02484210804104805}}, {"name": "conv128_down/conv2/conv/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 9.031058637304466e-10, "min": -1.1650065642122761e-07}}, {"name": "conv128_down/conv2/scale/weights", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.027663578706629135, "min": 3.1111555099487305}}, {"name": "conv128_down/conv2/scale/biases", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.008878476946961646, "min": -1.029903325847551}}, {"name": "conv128_1/conv1/conv/filters", "shape": [3, 3, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00022380667574265425, "min": -0.032899581334170175}}, {"name": "conv128_1/conv1/conv/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 4.4147297756478345e-10, "min": -5.253528433020923e-08}}, {"name": "conv128_1/conv1/scale/weights", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.013599334978589825, "min": 3.634530782699585}}, {"name": "conv128_1/conv1/scale/biases", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.014059314073300829, "min": -1.4059314073300828}}, {"name": "conv128_1/conv2/conv/filters", "shape": [3, 3, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00021715293474057143, "min": -0.02909849325523657}}, {"name": "conv128_1/conv2/conv/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 9.887046963276768e-10, "min": -1.1370104007768284e-07}}, {"name": "conv128_1/conv2/scale/weights", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.029993299409454943, "min": 3.630716562271118}}, {"name": "conv128_1/conv2/scale/biases", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00782704236460667, "min": -0.7200878975438136}}, {"name": "conv128_2/conv1/conv/filters", "shape": [3, 3, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00017718105923895743, "min": -0.022324813464108636}}, {"name": "conv128_2/conv1/conv/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 3.567012027797675e-10, "min": -5.243507680862582e-08}}, {"name": "conv128_2/conv1/scale/weights", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.007940645778880399, "min": 4.927767753601074}}, {"name": "conv128_2/conv1/scale/biases", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.015933452867994122, "min": -1.5614783810634238}}, {"name": "conv128_2/conv2/conv/filters", "shape": [3, 3, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0001451439717236687, "min": -0.01712698866339291}}, {"name": "conv128_2/conv2/conv/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 1.0383988570966347e-09, "min": -1.2356946399449953e-07}}, {"name": "conv128_2/conv2/scale/weights", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.02892604528688917, "min": 4.750600814819336}}, {"name": "conv128_2/conv2/scale/biases", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00797275748907351, "min": -0.7414664464838364}}, {"name": "conv256_down/conv1/conv/filters", "shape": [3, 3, 128, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0002698827827093648, "min": -0.03994265184098599}}, {"name": "conv256_down/conv1/conv/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 5.036909834755123e-10, "min": -6.396875490139006e-08}}, {"name": "conv256_down/conv1/scale/weights", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.014870181738161573, "min": 4.269900798797607}}, {"name": "conv256_down/conv1/scale/biases", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.022031106200872685, "min": -3.1063859743230484}}, {"name": "conv256_down/conv2/conv/filters", "shape": [3, 3, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00046430734150549946, "min": -0.03946612402796745}}, {"name": "conv256_down/conv2/conv/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 6.693064577513153e-10, "min": -7.630093618364995e-08}}, {"name": "conv256_down/conv2/scale/weights", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.03475512242784687, "min": 3.608360528945923}}, {"name": "conv256_down/conv2/scale/biases", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01290142021927179, "min": -1.1482263995151893}}, {"name": "conv256_1/conv1/conv/filters", "shape": [3, 3, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00037147209924810076, "min": -0.04234781931428348}}, {"name": "conv256_1/conv1/conv/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 3.2105515457510146e-10, "min": -3.467395669411096e-08}}, {"name": "conv256_1/conv1/scale/weights", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.043242172166412955, "min": 5.28542947769165}}, {"name": "conv256_1/conv1/scale/biases", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01643658619300992, "min": -1.3149268954407936}}, {"name": "conv256_1/conv2/conv/filters", "shape": [3, 3, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0003289232651392619, "min": -0.041773254672686264}}, {"name": "conv256_1/conv2/conv/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 9.13591691187321e-10, "min": -1.2333487831028833e-07}}, {"name": "conv256_1/conv2/scale/weights", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0573908618852204, "min": 4.360693454742432}}, {"name": "conv256_1/conv2/scale/biases", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0164216583850337, "min": -1.3958409627278647}}, {"name": "conv256_2/conv1/conv/filters", "shape": [3, 3, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00010476927912118389, "min": -0.015610622589056398}}, {"name": "conv256_2/conv1/conv/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 2.418552539068639e-10, "min": -2.539480166022071e-08}}, {"name": "conv256_2/conv1/scale/weights", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.06024209564807368, "min": 6.598613739013672}}, {"name": "conv256_2/conv1/scale/biases", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01578534350675695, "min": -1.1049740454729864}}, {"name": "conv256_2/conv2/conv/filters", "shape": [3, 3, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 5.543030908002573e-05, "min": -0.007427661416723448}}, {"name": "conv256_2/conv2/conv/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 1.0822061852320308e-09, "min": -1.515088659324843e-07}}, {"name": "conv256_2/conv2/scale/weights", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.04302893993901272, "min": 2.2855491638183594}}, {"name": "conv256_2/conv2/scale/biases", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006792667566561232, "min": -0.8083274404207865}}, {"name": "conv256_down_out/conv1/conv/filters", "shape": [3, 3, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.000568966465253456, "min": -0.05632768006009214}}, {"name": "conv256_down_out/conv1/conv/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 4.5347887884881677e-10, "min": -6.530095855422961e-08}}, {"name": "conv256_down_out/conv1/scale/weights", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.017565592597512638, "min": 4.594101905822754}}, {"name": "conv256_down_out/conv1/scale/biases", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.04850864223405427, "min": -6.306123490427055}}, {"name": "conv256_down_out/conv2/conv/filters", "shape": [3, 3, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0003739110687199761, "min": -0.06954745878191555}}, {"name": "conv256_down_out/conv2/conv/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 1.2668428328152895e-09, "min": -2.2549802424112154e-07}}, {"name": "conv256_down_out/conv2/scale/weights", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.04351314469879749, "min": 4.31956672668457}}, {"name": "conv256_down_out/conv2/scale/biases", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.021499746921015722, "min": -1.2039858275768804}}, {"name": "fc", "shape": [256, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.000357687911566566, "min": -0.04578405268052045}}], "paths": ["face_recognition_model-shard1", "face_recognition_model-shard2"]}]