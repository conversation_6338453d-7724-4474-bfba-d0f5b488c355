"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import React, { useEffect, useState, useCallback, useMemo, useRef } from "react";
import { uwhizQuestionForStudent, getQuizState, updateQuizState, clearQuizState } from "@/services/uwhizQuestionForStudentApi";
import { saveUwhizAnswer as saveAnswer } from "@/services/uwhizSaveExamAnswerApi";
import { saveTerminatedStudent as saveTermination, countUwhizAttemp } from "@/services/quizTeminationApi";
import { useParams, useRouter } from "next/navigation";
import { Loader, Clock } from "lucide-react";
import { toast } from "sonner";
import Image from "next/image";
import examLogo from '../../../../public/uwhizExam.png';
import ExamCameraMonitoring from "@/components/ExamCameraMonitoring";
import { getStudentDetail } from "@/services/studentDetailServiceApi";

interface Question {
  id: string;
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  subject: string;
  level: string;
  timePerQuestion: number;
  exam_name: string;
}

interface UserAnswer {
  questionId: string;
  selectedAnswer: string;
}

interface QuizHeaderProps {
  examName: string;
}

const QuizHeader: React.FC<QuizHeaderProps> = React.memo(
  ({ examName }) => {
    return (
      <header className="fixed top-0 left-0 right-0 z-20 py-2 px-4 sm:px-6 sm:py-3 flex flex-col sm:flex-row items-center justify-between bg-black text-white shadow-md">
        <div className="flex items-center justify-center gap-3">
          <Image
            height={60}
            width={60}
            src={examLogo.src}
            alt="Uwhiz Logo"
            quality={100}
            className="object-contain sm:h-20 sm:w-20"
          />
          <h1 className="text-lg sm:text-2xl font-bold tracking-tight">{examName.toUpperCase()}</h1>
        </div>
      </header>
    );
  }
);
QuizHeader.displayName = "QuizHeader";

export default function QuizPage() {
  const router = useRouter();
  const { examId } = useParams();
  const examIdStr = Array.isArray(examId) ? examId[0] : examId || "";

  let studentId: string | null = null;
  try {
    const data = localStorage.getItem("student_data");
    studentId = data ? JSON.parse(data).id : null;
  } catch (error) {
    console.error("Error retrieving studentId:", error);
    studentId = null;
  }

  const [isLoginDialogOpen, setIsLoginDialogOpen] = useState(!studentId);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isProfileDialogOpen, setIsProfileDialogOpen] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [showWarning, setShowWarning] = useState(false);
  const [showTermination, setShowTermination] = useState(false);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [userAnswers, setUserAnswers] = useState<UserAnswer[]>([]);
  const [isQuizCompleted, setIsQuizCompleted] = useState(false);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [isApiCallPending, setIsApiCallPending] = useState(false);
  const tickSoundRef = useRef<HTMLAudioElement | null>(null);
  const [terminationMessage, setTerminationMessage] = useState<string>("");
  const [violationCount, setViolationCount] = useState(0);
  const [isCameraReady, setIsCameraReady] = useState(false);

  // Initialize violation counts from database
  const initializeViolationCounts = async () => {
    if (!studentId || !examIdStr) {
      return 0;
    }
    try {
      const count = await countUwhizAttemp(studentId, Number(examIdStr));
      return typeof count === 'number' ? count : 0;
    } catch (error) {
      console.error("Failed to fetch violation count:", error);
      return 0;
    }
  };

  useEffect(() => {
    const fetchCounts = async () => {
      const count = await initializeViolationCounts();
      setViolationCount(count);
    };
    fetchCounts();
  }, [studentId, examIdStr]);

  useEffect(() => {
    if (violationCount >= 3) {
      setShowTermination(true);
      setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
    }
  }, [violationCount]);

  // Initialize clock-tick sound
  useEffect(() => {
    tickSoundRef.current = new Audio("/clock-ticking-sound-effect.mp3");
    tickSoundRef.current.loop = true;
    return () => {
      if (tickSoundRef.current) {
        tickSoundRef.current.pause();
        tickSoundRef.current = null;
      }
    };
  }, []);

  // Play or pause clock-tick sound only in the last 5 seconds
  useEffect(() => {
    if (
      questions.length > 0 &&
      timeLeft <= 5 &&
      timeLeft > 0 &&
      !isDialogOpen &&
      !showTermination &&
      !isQuizCompleted &&
      !isLoginDialogOpen &&
      !isProfileDialogOpen &&
      tickSoundRef.current
    ) {
      tickSoundRef.current.play().catch((error) => {
        console.error("Failed to play tick sound:", error);
      });
    } else if (tickSoundRef.current) {
      tickSoundRef.current.pause();
    }
  }, [timeLeft, questions, isDialogOpen, showTermination, isQuizCompleted, isLoginDialogOpen, isProfileDialogOpen]);

  // Handle answer selection
  const handleAnswerSelect = useCallback((answer: string, optionKey: string) => {
    setSelectedAnswer(optionKey);
    setUserAnswers((prev) => {
      const existingAnswer = prev.find(
        (ans) => ans.questionId === questions[currentQuestionIndex].id
      );
      if (existingAnswer) {
        return prev.map((ans) =>
          ans.questionId === questions[currentQuestionIndex].id
            ? { ...ans, selectedAnswer: optionKey }
            : ans
        );
      }
      return [
        ...prev,
        { questionId: questions[currentQuestionIndex].id, selectedAnswer: optionKey },
      ];
    });
  }, [currentQuestionIndex, questions]);

  const handleNextQuestion = useCallback(async () => {
    if (isSubmitted) return;

    if (studentId && examIdStr && selectedAnswer) {
      setIsSubmitted(true);
      try {
        const response = await saveAnswer({
          studentId,
          examId: Number(examIdStr),
          questionId: questions[currentQuestionIndex].id,
          selectedAns: selectedAnswer,
        });
        if (!response.success) {
          // toast.error(response.error);
        } else {
          toast.success("Answer saved successfully!");
        }
      } catch (error: unknown) {
        toast.error("Failed to save answer.", { description: error instanceof Error ? error.message : "Unknown error" });
      } finally {
        setIsSubmitted(false);
      }
    } else {
      toast.info("Question skipped.");
    }

    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedAnswer(null);
    } else {
      setIsQuizCompleted(true);
    }
  }, [currentQuestionIndex, questions, selectedAnswer, studentId, examIdStr]);

  // Timer logic
  useEffect(() => {
    if (questions.length > 0 && timeLeft > 0 && !isDialogOpen && !showTermination && !isLoginDialogOpen && !isProfileDialogOpen) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          const newTime = prev - 1;
          if (newTime <= 0) {
            clearInterval(timer);
            handleNextQuestion();
            return 0;
          }
          return newTime;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [timeLeft, questions, currentQuestionIndex, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen, handleNextQuestion]);

  // Reset timer when moving to a new question
  useEffect(() => {
    if (questions.length > 0 && !isDialogOpen && !showTermination && !isLoginDialogOpen && !isProfileDialogOpen) {
      const newTime = questions[currentQuestionIndex]?.timePerQuestion || 30;
      setTimeLeft(newTime);
    }
  }, [currentQuestionIndex, questions, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen]);

  // Modified: Wait for isCameraReady before opening dialog
  const fetchQuizState = useCallback(async () => {
    if (!studentId || !examIdStr) {
      setIsLoginDialogOpen(true);
      return;
    }

    try {
      const studentResponse = await getStudentDetail(studentId);
      if (!studentResponse.success) {
        toast.error(studentResponse.error);
        setIsProfileDialogOpen(true);
        return;
      }

      const { classroom, medium } = studentResponse.data;

      const cachedState = await getQuizState(studentId, examIdStr);
      if (cachedState) {
        setQuestions(cachedState.questions);
        setCurrentQuestionIndex(cachedState.currentQuestionIndex);
        setUserAnswers(cachedState.userAnswers);
        setTimeLeft(cachedState.questions[cachedState.currentQuestionIndex]?.timePerQuestion || 30);
        if (cachedState.currentQuestionIndex >= cachedState.questions.length) {
          setIsQuizCompleted(true);
        } else {
          setIsDialogOpen(true);
        }
        return;
      }

      const questionResponse = await uwhizQuestionForStudent(
        studentId,
        medium,
        classroom,
        examIdStr
      );
      if (questionResponse && Array.isArray(questionResponse)) {
        setQuestions(questionResponse);
        setTimeLeft(questionResponse[0]?.timePerQuestion || 30);
        setIsDialogOpen(true);
      } else {
        toast.error("No questions found or invalid response.");
      }
    } catch (error: unknown) {
      if (error instanceof Error && error.message.startsWith("{")) {
        try {
          const parsedError = JSON.parse(error.message);
          if (parsedError.status === 403 || parsedError.status === 500) {
            setTerminationMessage(parsedError.message || "Your quiz has been terminated due to multiple cheating attempts.");
            setShowTermination(true);
            toast.error(parsedError.message || "Your quiz has been terminated due to multiple cheating attempts.");
            return;
          }
        } catch {
          toast.error("Failed to fetch quiz state.");
        }
      }
      const errorMessage = `Failed to fetch data: ${error instanceof Error ? error.message : "Unknown error"}`;
      toast.error(errorMessage);
    }
  }, [studentId, examIdStr, isCameraReady]);

  useEffect(() => {
    if (studentId && examIdStr) {
      fetchQuizState();
    } else {
      setIsLoginDialogOpen(true);
    }
  }, [studentId, examIdStr, fetchQuizState]);

  // Sync state to server on changes
  useEffect(() => {
    if (questions.length > 0 && !isDialogOpen && !showTermination && studentId && examIdStr && !isLoginDialogOpen && !isProfileDialogOpen) {
      updateQuizState(studentId, examIdStr, {
        questions,
        currentQuestionIndex,
        userAnswers,
      }).catch((error: unknown) => {
        toast.error("Failed to sync quiz progress.", { description: error instanceof Error ? error.message : "Unknown error" });
      });
    }
  }, [currentQuestionIndex, questions, studentId, examIdStr, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen]);

  // Utility function to exit full-screen mode with browser compatibility
  const exitFullScreen = async (maxAttempts = 3, attempt = 1): Promise<boolean> => {
    try {
      if (document.fullscreenElement || (document as any).webkitFullscreenElement || (document as any).mozFullScreenElement) {
        if (document.exitFullscreen) {
          await document.exitFullscreen();
        } else if ((document as any).webkitExitFullscreen) {
          await (document as any).webkitExitFullscreen();
        } else if ((document as any).mozCancelFullScreen) {
          await (document as any).mozCancelFullScreen();
        }
        await new Promise(resolve => setTimeout(resolve, 100));
        if (!document.fullscreenElement && !(document as any).webkitFullscreenElement && !(document as any).mozFullScreenElement) {
          return true;
        }
        if (attempt < maxAttempts) {
          return await exitFullScreen(maxAttempts, attempt + 1);
        }
        throw new Error("Max attempts reached");
      }
      return true;
    } catch (err: unknown) {
      console.error(`Failed to exit full-screen mode (attempt ${attempt}):`, err);
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return await exitFullScreen(maxAttempts, attempt + 1);
      }
      toast.error("Failed to exit full-screen mode. Please press Esc to exit manually.");
      return false;
    }
  };

  // Clear cache and exit full-screen on quiz completion
  useEffect(() => {
    if (isQuizCompleted && studentId && examIdStr) {
      exitFullScreen().then((success) => {
        if (success) {
          clearQuizState(studentId, examIdStr).catch((error: unknown) => {
            console.error("Failed to clear quiz state:", error);
          });
        }
      });
    }
  }, [isQuizCompleted, studentId, examIdStr]);

  // Clear cache and exit full-screen on termination
  useEffect(() => {
    if (showTermination && studentId && examIdStr) {
      exitFullScreen().then((success) => {
        console.log("Quiz terminated, cache cleared and full-screen exited.", success);
      });
    }
  }, [showTermination, studentId, examIdStr]);

  // Function to enter full-screen mode
  const enterFullScreen = () => {
    const element = document.documentElement;
    if (element.requestFullscreen) {
      element.requestFullscreen().catch((err: unknown) => console.error("Failed to enter fullscreen:", err));
    }
  };

  // Handle dialog confirmation to start quiz in full-screen
  const handleStartQuiz = () => {
    setIsDialogOpen(false);
    enterFullScreen();
    if (questions.length > 0) {
      setTimeLeft(questions[currentQuestionIndex]?.timePerQuestion || 30);
    }
  };

  // Handle navigation to profile page with quiz parameter
  const handleGoToProfile = () => {
    router.push("/student/profile?quiz=true&examId=" + examId);
  };

  // Handle face-api.js violations
  const handleViolation = useCallback(async (reason: string) => {
    if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending) return;

    setIsApiCallPending(true);
    try {
      await saveTermination({
        examId: Number(examIdStr),
        studentId,
        reason,
      });
      const updatedCount = await countUwhizAttemp(studentId as string, Number(examIdStr));
      setViolationCount(updatedCount);
      if (updatedCount === 1) {
        setShowWarning(true);
        toast.warning(`${reason} detected.`);
      } else if (updatedCount === 2) {
        setShowWarning(true);
        toast.warning(`${reason} detected. One more violation will terminate the quiz.`);
      } else if (updatedCount >= 3) {
        setShowTermination(true);
        setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
        toast.error("Quiz terminated due to multiple cheating attempts.");
      }
    } catch (error: unknown) {
      toast.error("Failed to save violation record.", { description: error instanceof Error ? error.message : "Unknown error" });
    } finally {
      setIsApiCallPending(false);
    }
  }, [examIdStr, studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]);

  // Handle keyboard restrictions
  const handleKeyDown = useCallback(
    async (event: KeyboardEvent) => {
      if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending) return;

      const restrictedKeys = ["Alt", "Control", "Tab", "Shift", "Enter"];
      const functionKeys = ["F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12"];
      const isDevToolsShortcut =
        (event.ctrlKey && event.shiftKey && (event.key === "I" || event.key === "J" || event.key === "C")) ||
        (event.metaKey && event.altKey && event.key === "I") ||
        event.key === "F12";
      const isCopyShortcut =
        (event.ctrlKey || event.metaKey) && (event.key === "c" || event.key === "C");

      if (
        restrictedKeys.includes(event.key) ||
        functionKeys.includes(event.key) ||
        isDevToolsShortcut ||
        isCopyShortcut
      ) {
        event.preventDefault();
        if (isCopyShortcut) {
          toast.warning("Copying is disabled during the quiz.");
          return;
        }
        if (!studentId) {
          setViolationCount(0);
          return;
        }
        setIsApiCallPending(true);
        try {
          const violationType = isDevToolsShortcut
            ? "DevTools shortcut"
            : functionKeys.includes(event.key)
              ? `Function key "${event.key}"`
              : `Restricted key "${event.key}"`;
          await saveTermination({
            examId: Number(examIdStr),
            studentId,
            reason: violationType,
          });
          const updatedCount = await countUwhizAttemp(studentId, Number(examIdStr));
          setViolationCount(updatedCount);
          if (updatedCount === 1) {
            setShowWarning(true);
            toast.warning(`${violationType} detected.`);
          } else if (updatedCount === 2) {
            setShowWarning(true);
            toast.warning(`${violationType} detected. One more violation will terminate the quiz.`);
          } else if (updatedCount >= 3) {
            setShowTermination(true);
            setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
            toast.error("Quiz terminated due to multiple cheating attempts.");
          }
        } catch (error: unknown) {
          toast.error("Failed to save termination record.", { description: error instanceof Error ? error.message : "Unknown error" });
        } finally {
          setIsApiCallPending(false);
        }
      }
    },
    [examIdStr, studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]
  );

  // Handle tab switch detection and logic
  const handleVisibilityChange = useCallback(async () => {
    if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending) return;

    if (document.hidden) {
      setIsApiCallPending(true);
      try {
        await saveTermination({
          examId: Number(examIdStr),
          studentId,
          reason: "Tab switch",
        });
        if (!studentId) {
          setViolationCount(0);
          return;
        }
        const updatedCount = await countUwhizAttemp(studentId as string, Number(examIdStr));
        setViolationCount(updatedCount);
        if (updatedCount === 1) {
          setShowWarning(true);
          toast.warning("Tab switch detected.");
        } else if (updatedCount === 2) {
          setShowWarning(true);
          toast.warning("Again tab switch detected. One more violation will terminate the quiz.");
        } else if (updatedCount >= 3) {
          setShowTermination(true);
          setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
          toast.error("Quiz terminated due to multiple cheating attempts.");
        }
      } catch (error: unknown) {
        toast.error("Failed to save termination record.", { description: error instanceof Error ? error.message : "Unknown error" });
      } finally {
        setIsApiCallPending(false);
      }
    }
  }, [examIdStr, studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]);

  // Handle context menu
  const handleContextMenu = useCallback(
    async (event: MouseEvent) => {
      if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending) return;
      event.preventDefault();
      toast.warning("Right-click is disabled during the quiz.");
    },
    [examIdStr, studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]
  );

  // Handle window focus loss
  const handleWindowBlur = useCallback(async () => {
    if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending) return;

    setIsApiCallPending(true);
    try {
      await saveTermination({
        examId: Number(examIdStr),
        studentId,
        reason: "Window blur",
      });
      const updatedCount = await countUwhizAttemp(studentId as string, Number(examIdStr));
      setViolationCount(updatedCount);
      if (updatedCount === 1) {
        setShowWarning(true);
        toast.warning("Window focus lost.");
      } else if (updatedCount === 2) {
        setShowWarning(true);
        toast.warning("Window focus lost again. One more violation will terminate the quiz.");
      } else if (updatedCount >= 3) {
        setShowTermination(true);
        setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
        toast.error("Quiz terminated due to multiple cheating attempts.");
      }
    } catch (error: unknown) {
      toast.error("Failed to save termination record.", { description: error instanceof Error ? error.message : "Unknown error" });
    } finally {
      setIsApiCallPending(false);
    }
  }, [examIdStr, studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]);

  // Handle full-screen exit
  const handleFullScreenChange = useCallback(async () => {
    if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending) return;

    if (!document.fullscreenElement) {
      setIsApiCallPending(true);
      try {
        await saveTermination({
          examId: Number(examIdStr),
          studentId,
          reason: "Full-screen exit",
        });
        if (!studentId) {
          setViolationCount(0);
          return;
        }
        const updatedCount = await countUwhizAttemp(studentId as string, Number(examIdStr));
        setViolationCount(updatedCount);
        if (updatedCount === 1) {
          setShowWarning(true);
          toast.warning("You have exited full-screen mode.");
        } else if (updatedCount === 2) {
          setShowWarning(true);
          toast.warning("Again you have exited full-screen mode. One more violation will terminate the quiz.");
        } else if (updatedCount >= 3) {
          setShowTermination(true);
          setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
          toast.error("Quiz terminated due to multiple cheating attempts.");
        }
      } catch (error: unknown) {
        toast.error("Failed to save termination record.", { description: error instanceof Error ? error.message : "Unknown error" });
      } finally {
        setIsApiCallPending(false);
      }
    }
  }, [examIdStr, studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]);

  // Handle navigation to home page
  const handleGoHome = async () => {
    setShowTermination(false);
    if (studentId && examIdStr) {
      try {
        await clearQuizState(studentId, examIdStr);
      } catch (error: unknown) {
        console.error("Failed to clear quiz state:", error);
      }
    }
    const isFullScreen = document.fullscreenElement || (document as any).webkitFullscreenElement || (document as any).mozFullScreenElement;
    if (isFullScreen) {
      const success = await exitFullScreen();
      if (success) {
        router.push("/uwhiz");
      } else {
        toast.warning("Could not exit full-screen mode automatically. Please press Esc to exit manually.");
        router.push("/uwhiz");
      }
    } else {
      router.push("/uwhiz");
    }
  };

  // Set up event listeners only when dialogs are closed
  useEffect(() => {
    if (!isDialogOpen && !isLoginDialogOpen && !isProfileDialogOpen && !showTermination) {
      document.addEventListener("visibilitychange", handleVisibilityChange);
      document.addEventListener("keydown", handleKeyDown);
      window.addEventListener("blur", handleWindowBlur);
      document.addEventListener("contextmenu", handleContextMenu);
      document.addEventListener("fullscreenchange", handleFullScreenChange);
    }
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      document.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("blur", handleWindowBlur);
      document.removeEventListener("contextmenu", handleContextMenu);
      document.removeEventListener("fullscreenchange", handleFullScreenChange);
    };
  }, [
    handleVisibilityChange,
    handleKeyDown,
    handleWindowBlur,
    handleContextMenu,
    handleFullScreenChange,
    isDialogOpen,
    isLoginDialogOpen,
    isProfileDialogOpen,
    showTermination,
  ]);

  // Format time for each question
  const formatTime = useCallback((seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }, []);

  // Exam name
  const examName = useMemo(() => {
    return questions.length > 0 ? questions[0].exam_name : "Uwhiz - Super kids";
  }, [questions]);

  // Progress
  const progress = useMemo(() => {
    return questions.length > 0
      ? ((currentQuestionIndex + 1) / questions.length) * 100
      : 0;
  }, [currentQuestionIndex, questions]);

  // Button styles
  const getButtonClass = (optionKey: string) => {
    return `w-full h-auto min-h-[60px] sm:min-h-[80px] whitespace-normal text-wrap font-medium rounded-lg py-3 sm:py-4 text-sm sm:text-lg text-gray-700 hover:bg-orange-100 hover:border-orange-500 transition-all duration-200 flex items-start justify-start gap-3 px-3 sm:px-6 shadow-sm border border-gray-200 bg-white ${selectedAnswer === optionKey ? "bg-orange-100 border-orange-500" : ""}`;
  };

  if (isLoginDialogOpen) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100 text-gray-900">
        <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96">
          <h2 className="text-lg sm:text-2xl font-bold mb-4">Login Required</h2>
          <p className="mb-4 text-sm sm:text-base text-gray-600">
            Please log in as a student to access the quiz.
          </p>
          <Button
            onClick={() => router.push(`/student/login?redirect=/uwhiz-exam/${examId}`)}
            className="bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all"
          >
            Login to Continue
          </Button>
        </div>
      </div>
    );
  }

  if (isProfileDialogOpen) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100 text-gray-900">
        <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96">
          <h2 className="text-lg sm:text-2xl font-bold mb-4">Complete Your Profile</h2>
          <p className="mb-4 text-sm sm:text-base text-gray-600">
            Your profile is incomplete. Please complete your profile to proceed.
          </p>
          <Button
            onClick={handleGoToProfile}
            className="bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all"
          >
            Complete Profile
          </Button>
        </div>
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100 text-gray-900">
        <p className="text-base sm:text-xl font-medium mr-4">Loading questions...</p>
        <Loader className="w-5 h-5 sm:w-8 sm:h-8 animate-spin text-customOrange" />
      </div>
    );
  }

  if (isQuizCompleted) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100 text-gray-900">
        <div className="text-center p-4 sm:p-6 bg-white rounded-lg shadow-xl max-w-md w-full">
          <h1 className="text-2xl sm:text-4xl font-bold text-customOrange mb-4">Quiz Completed!</h1>
          <p className="text-base sm:text-xl mb-4">Your exam submitted successfully.</p>
          <Button
            className="bg-customOrange text-white px-4 py-2 sm:px-6 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg transition-all"
            onClick={handleGoHome}
          >
            Go To Home
          </Button>
        </div>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];

  return (
    <div className="flex flex-col min-h-screen bg-gray-100 text-gray-900">
      {/* Initial confirmation dialog for full-screen */}
      {isDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-3/4 md:w-1/2 max-h-[80vh] flex flex-col overflow-y-auto">
            <h2 className="text-lg sm:text-2xl font-bold mb-4">Start Quiz</h2>
         
            {studentId && (
              <ExamCameraMonitoring
                studentId={studentId}
                shouldSavePhoto={true}
                examId={Number(examIdStr)}
                isExamActive={!showTermination && !isQuizCompleted}
                onCameraError={(error) => {
                  console.error('Camera error:', error);
                  setIsCameraReady(false);
                }}
                onCameraStatus={(isActive) => {
                  setIsCameraReady(isActive);
                }}
                onViolation={handleViolation}
              />
            )}
            <div className="flex-1 overflow-y-auto pr-2 mb-4 text-sm sm:text-base">
              <p className="font-semibold mb-2">Instructions:</p>
              <ul className="list-disc list-inside mb-4 text-gray-600">
                <li>Do not switch tabs during the quiz.</li>
                <li>Do not use restricted keys (Alt, Ctrl, Tab, Shift, Function keys).</li>
                <li>Do not open Developer Tools.</li>
                <li>Do not exit full-screen mode.</li>
                <li>Do not interact with other windows or applications.</li>
                <li>Do not change the screen or minimize the quiz window.</li>
                <li>Do not receive or make calls during the quiz.</li>
                <li>Do not use split screen or floating windows on your device.</li>
              </ul>
              <p className="font-semibold mb-2">સૂચનાઓ (ગુજરાતી):</p>
              <ul className="list-disc list-inside text-gray-600">
                <li>ક્વિઝ દરમિયાન ટેબ બદલશો નહીં.</li>
                <li>પ્રતિબંધિત કીઓ (ઓલ્ટ, કંટ્રોલ, ટેબ, શિફ્ટ, ફંક્શન કીઓ) નો ઉપયોગ કરશો નહીં.</li>
                <li>ડેવલપર ટૂલ્સ ખોલશો નહીં.</li>
                <li>ક્વિઝ દરમિયાન જમણું-ક્લિક કરશો નહીં.</li>
                <li>ફુલ-સ્ક્રીન મોડમાંથી બહાર નીકળશો નહીં.</li>
                <li>અન્ય વિન્ડોઝ અથવા એપ્લિકેશન્સ સાથે સંપર્ક કરશો નહીં.</li>
                <li>સ્ક્રીન બદલશો નહીં અથવા ક્વિઝ વિન્ડો નાની કરશો નહીં.</li>
                <li>ક્વિઝ દરમિયાન કૉલ રિસીવ કરશો નહીં અથવા કૉલ કરશો નહીં.</li>
                <li>તમારા ડિવાઇસ પર સ્પ્લિટ સ્ક્રીન અથવા ફ્લોટિંગ વિન્ડોઝનો ઉપયોગ કરશો નહીં.</li>
              </ul>
            </div>
            <Button
              onClick={handleStartQuiz}
              className="bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all"
              disabled={!isCameraReady}
            >
              {isCameraReady ? "Start Quiz" : "Initializing Face Recognition..."}
            </Button>
          </div>
        </div>
      )}

      {/* Warning dialog for first violation */}
      {showWarning && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96">
            <h2 className="text-lg sm:text-2xl font-bold mb-4 text-customOrange">Warning</h2>
            <p className="mb-4 text-sm sm:text-base text-gray-600">
              You have performed a restricted action. Repeating this will terminate the quiz.
            </p>
            <Button
              onClick={() => setShowWarning(false)}
              className="bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all"
            >
              OK
            </Button>
          </div>
        </div>
      )}

      {/* Termination dialog for second violation */}
      {showTermination && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96">
            <h2 className="text-lg sm:text-2xl font-bold mb-4 text-red-500">Quiz Terminated</h2>
            <p className="mb-4 text-sm sm:text-base text-gray-600">
              {terminationMessage || "Your quiz has been terminated due to multiple cheating attempts."}
            </p>
            <Button
              onClick={handleGoHome}
              className="bg-red-500 text-white px-4 py-2 rounded-full hover:bg-red-600 text-sm sm:text-base w-full transition-all"
            >
              Go to Home
            </Button>
          </div>
        </div>
      )}

      {!isDialogOpen && !isLoginDialogOpen && !isProfileDialogOpen && (
        <>
          <QuizHeader examName={examName} />

          {/* Camera Monitoring Component */}
         {studentId && (
            <ExamCameraMonitoring
            shouldSavePhoto={true}
              studentId={studentId}
              examId={Number(examIdStr)}
              isExamActive={!showTermination && !isQuizCompleted}
              onCameraError={(error) => {
                console.error('Camera error:', error);
                setIsCameraReady(false);
              }}
              onCameraStatus={(isActive) => {
                setIsCameraReady(isActive);
              }}
              onViolation={handleViolation}
            />
          )}

          {/* Progress Bar */}
          <div className="fixed top-[60px] sm:top-[80px] left-0 right-0 z-10 w-full h-1.5 bg-gray-200">
            <div
              className="h-1.5 bg-customOrange rounded-r-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="flex-1 flex flex-col items-center justify-center px-4 sm:px-6 pt-[80px] sm:pt-[100px] pb-[48px] sm:pb-[64px] min-h-screen">
            {/* Main Content Wrapper */}
            <div className="flex flex-col items-center justify-center w-full max-w-3xl">
              {/* Timer above question */}
              <div className="mt-2 sm:mt-4 mb-4 sm:mb-6 flex items-center gap-2 bg-gray-800/80 px-4 sm:px-6 py-2 rounded-full shadow-lg">
                <Clock className="w-5 h-5 sm:w-6 sm:h-6 text-customOrange animate-pulse" />
                <span className="text-lg sm:text-2xl font-bold text-customOrange">
                  {formatTime(timeLeft)}
                </span>
              </div>
              {/* Question Card */}
              <div className="w-full text-center flex flex-col items-center">
                <div className="flex justify-center mb-3 sm:mb-4">
                  <span className="text-xs sm:text-base font-semibold text-customOrange bg-orange-100 px-2 sm:px-3 py-1 rounded-full shadow-sm">
                    Question {currentQuestionIndex + 1} of {questions.length}
                  </span>
                </div>
                <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl mb-6 w-full max-h-[60vh] sm:max-h-[70vh] overflow-y-auto">
                  <h2
                    className="text-lg sm:text-2xl md:text-3xl font-bold text-gray-800 mb-4 sm:mb-6"
                    dangerouslySetInnerHTML={{ __html: currentQuestion.question }}
                  ></h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 w-full">
                    <Button
                      variant="outline"
                      className={getButtonClass("optionOne")}
                      onClick={() => handleAnswerSelect(currentQuestion.optionOne, "optionOne")}
                      disabled={showTermination}
                    >
                      <span className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0">
                        A
                      </span>
                      <span className="flex-1 text-left whitespace-normal break-words">{currentQuestion.optionOne}</span>
                    </Button>
                    <Button
                      variant="outline"
                      className={getButtonClass("optionTwo")}
                      onClick={() => handleAnswerSelect(currentQuestion.optionTwo, "optionTwo")}
                      disabled={showTermination}
                    >
                      <span className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0">
                        B
                      </span>
                      <span className="flex-1 text-left whitespace-normal break-words">{currentQuestion.optionTwo}</span>
                    </Button>
                    <Button
                      variant="outline"
                      className={getButtonClass("optionThree")}
                      onClick={() => handleAnswerSelect(currentQuestion.optionThree, "optionThree")}
                      disabled={showTermination}
                    >
                      <span className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0">
                        C
                      </span>
                      <span className="flex-1 text-left whitespace-normal break-words">{currentQuestion.optionThree}</span>
                    </Button>
                    <Button
                      variant="outline"
                      className={getButtonClass("optionFour")}
                      onClick={() => handleAnswerSelect(currentQuestion.optionFour, "optionFour")}
                      disabled={showTermination}
                    >
                      <span className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0">
                        D
                      </span>
                      <span className="flex-1 text-left whitespace-normal break-words">{currentQuestion.optionFour}</span>
                    </Button>
                  </div>
                </div>
                {/* Next Button */}
                <Button
                  className="bg-customOrange text-white px-6 sm:px-8 py-2 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg font-semibold shadow-lg transform hover:scale-105 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={() => handleNextQuestion()}
                  disabled={showTermination || isSubmitted}
                >
                  {isSubmitted ? (currentQuestionIndex === questions.length - 1 ? "Finish" : "Next Question") : "Save Answer"}
                </Button>
              </div>
              {/* Footer */}
              <footer className="fixed bottom-0 left-0 right-0 bg-black text-white py-2 px-4 sm:px-6 flex items-center justify-center gap-1.5 sm:gap-2 text-xs sm:text-sm">
                <span>Powered by</span>
                <Image
                  src="/logo_black.png"
                  alt="Nalanda Logo"
                  height={20}
                  width={20}
                  className="object-contain sm:h-6 sm:w-6"
                />
                <span className="font-semibold">UEST EdTech</span>
              </footer>
            </div>
          </div>
        </>
      )}
    </div>
  );
}