import { z } from 'zod';

// Validation for coin transfer
export const transferCoinsSchema = z.object({
  recipientContact: z
    .string()
    .min(10, 'Contact number must be 10 digits')
    .max(10, 'Contact number must be 10 digits')
    .regex(/^\d+$/, 'Contact number must contain only digits'),
  amount: z
    .number()
    .min(1, 'Amount must be at least 1 coin')
    .max(10000, 'Amount cannot exceed 10,000 coins')
    .int('Amount must be a whole number'),
  reason: z
    .string()
    .max(200, 'Reason cannot exceed 200 characters')
    .optional(),
});

// Validation for admin adding coins
export const addCoinsSchema = z.object({
  studentId: z.string().uuid('Invalid student ID'),
  amount: z
    .number()
    .min(1, 'Amount must be at least 1 coin')
    .max(10000, 'Amount cannot exceed 10,000 coins')
    .int('Amount must be a whole number'),
  reason: z
    .string()
    .max(200, 'Reason cannot exceed 200 characters')
    .optional(),
});

// Validation for checking student
export const checkStudentSchema = z.object({
  contact: z
    .string()
    .min(10, 'Contact number must be 10 digits')
    .max(10, 'Contact number must be 10 digits')
    .regex(/^\d+$/, 'Contact number must contain only digits'),
});

export type TransferCoinsRequest = z.infer<typeof transferCoinsSchema>;
export type AddCoinsRequest = z.infer<typeof addCoinsSchema>;
export type CheckStudentRequest = z.infer<typeof checkStudentSchema>;
