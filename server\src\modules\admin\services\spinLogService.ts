import prisma from '@/config/prismaClient';
import { UserType } from '@prisma/client';

interface GetSpinLogsParams {
  page: number;
  limit: number;
  userType?: string;
  search?: string;
  activityType?: string;
}

interface SpinLogUser {
  id: string;
  name: string;
  email: string;
  profilePhoto?: string;
}

interface SpinLogEntry {
  id: string;
  userId: string;
  userType: UserType;
  activityType: string;
  createdAt: Date;
  user: SpinLogUser;
}

const getBuyerInfo = async (modelId: string, modelType: UserType) => {
  let buyerName = "Unknown User";
  let buyerEmail = "<EMAIL>";
  let profilePhoto = null;

  try {
    if (modelType === UserType.STUDENT) {
      const student = await prisma.student.findUnique({
        where: { id: modelId },
        select: {
          firstName: true,
          lastName: true,
          email: true,
          StudentProfile: {
            select: {
              photo: true,
            },
          },
        },
      });

      if (student) {
        buyerName = `${student.firstName} ${student.lastName || ""}`.trim();
        buyerEmail = student.email;
        profilePhoto = student.StudentProfile?.photo || null;
      }
    } else if (modelType === UserType.CLASS) {
      const classUser = await prisma.classes.findUnique({
        where: { id: modelId },
        select: {
          firstName: true,
          lastName: true,
          email: true,
          ClassAbout: {
            select: {
              profilePhoto: true,
            },
          },
        },
      });

      if (classUser) {
        buyerName = `${classUser.firstName} ${classUser.lastName || ""}`.trim();
        buyerEmail = classUser.email;
        profilePhoto = classUser.ClassAbout?.profilePhoto || null;
      }
    }
  } catch (error) {
    console.error("Error fetching buyer info:", error);
  }

  return { buyerName, buyerEmail, profilePhoto };
};

export const getSpinLogs = async (params: GetSpinLogsParams) => {
  const { page, limit, userType, search, activityType } = params;
  const skip = (page - 1) * limit;

  const where: any = {
    activityType: {
      contains: 'SPIN',
    },
  };

  if (userType && Object.values(UserType).includes(userType as UserType)) {
    where.userType = userType as UserType;
  }

  if (activityType) {
    where.activityType = activityType;
  }

  const [logs, total] = await Promise.all([
    prisma.userActivityLog.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
    }),
    prisma.userActivityLog.count({ where }),
  ]);

  // Get user details for each log entry
  const formattedLogs = await Promise.all(
    logs.map(async (log) => {
      const { buyerName, buyerEmail, profilePhoto } = await getBuyerInfo(log.userId, log.userType);
      
      return {
        id: log.id,
        userId: log.userId,
        userType: log.userType,
        activityType: log.activityType,
        createdAt: log.createdAt,
        user: {
          id: log.userId,
          name: buyerName,
          email: buyerEmail,
          profilePhoto,
        },
      };
    })
  );

  // Filter by search if provided
  let filteredLogs = formattedLogs;
  if (search) {
    const searchLower = search.toLowerCase();
    filteredLogs = formattedLogs.filter(log => 
      log.user.name.toLowerCase().includes(searchLower) ||
      log.user.email.toLowerCase().includes(searchLower) ||
      log.activityType.toLowerCase().includes(searchLower)
    );
  }

  const totalPages = Math.ceil(total / limit);

  return {
    logs: filteredLogs,
    pagination: {
      currentPage: page,
      totalPages,
      totalCount: total,
      limit,
    },
  };
};

export const getSpinStats = async () => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const thisWeek = new Date(today);
    thisWeek.setDate(thisWeek.getDate() - 7);

    const thisMonth = new Date(today);
    thisMonth.setMonth(thisMonth.getMonth() - 1);

    const [
      totalSpins,
      todaySpins,
      yesterdaySpins,
      weekSpins,
      monthSpins,
      freeSpins,
      paidSpins,
      coinRewards,
      itemRewards,
    ] = await Promise.all([
      // Total spins
      prisma.userActivityLog.count({
        where: { activityType: 'SPIN_COMPLETED' }
      }),
      
      // Today's spins
      prisma.userActivityLog.count({
        where: {
          activityType: 'SPIN_COMPLETED',
          createdAt: { gte: today }
        }
      }),
      
      // Yesterday's spins
      prisma.userActivityLog.count({
        where: {
          activityType: 'SPIN_COMPLETED',
          createdAt: { gte: yesterday, lt: today }
        }
      }),
      
      // This week's spins
      prisma.userActivityLog.count({
        where: {
          activityType: 'SPIN_COMPLETED',
          createdAt: { gte: thisWeek }
        }
      }),
      
      // This month's spins
      prisma.userActivityLog.count({
        where: {
          activityType: 'SPIN_COMPLETED',
          createdAt: { gte: thisMonth }
        }
      }),
      
      // Free spins (eligible checks that resulted in spins)
      prisma.userActivityLog.count({
        where: { activityType: 'SPIN_ELIGIBLE_CHECK' }
      }),
      
      // Paid spins
      prisma.userActivityLog.count({
        where: { activityType: 'SPIN_PAID' }
      }),
      
      // Coin rewards
      prisma.userActivityLog.count({
        where: { 
          activityType: { contains: 'SPIN_COINS_CREDITED' }
        }
      }),
      
      // Item rewards
      prisma.userActivityLog.count({
        where: { 
          activityType: { 
            in: ['SPIN_WON_LAPTOP', 'SPIN_WON_KEYCHAIN', 'SPIN_WON_UEST_SUPER_CARD', 'SPIN_WON_1_NOTEBOOK']
          }
        }
      }),
    ]);

    return {
      totalSpins,
      todaySpins,
      yesterdaySpins,
      weekSpins,
      monthSpins,
      freeSpins,
      paidSpins,
      coinRewards,
      itemRewards,
      successRate: totalSpins > 0 ? ((coinRewards + itemRewards) / totalSpins * 100).toFixed(2) : '0',
    };
  } catch (error) {
    console.error('Error getting spin stats:', error);
    throw new Error('Failed to get spin statistics');
  }
};
