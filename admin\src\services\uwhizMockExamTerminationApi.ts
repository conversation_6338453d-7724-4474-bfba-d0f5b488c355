import { axiosInstance } from '@/lib/axios';

export const getTerminatedStudentLogs = async (studentId: string) => {
  try {
    const response = await axiosInstance.get(`/mock-exam-terminate/${studentId}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });

    return {
      success: true,
      data: response.data.data,
    };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch termination logs: ${error.response?.data?.message || error.message}`,
    };
  }
};