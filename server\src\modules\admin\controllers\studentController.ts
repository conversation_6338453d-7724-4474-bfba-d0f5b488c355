import { Request, Response } from 'express';
import { sendSuccess, sendError } from '@/utils/response';
import prisma from '@/config/prismaClient';

/**
 * Get all student IDs for internal use (called by uwhiz server)
 */
export const getAllStudentIds = async (_req: Request, res: Response): Promise<void> => {
  try {
    const students = await prisma.student.findMany({
      select: {
        id: true,
        firstName: true,
        lastName: true
      }
    });
    sendSuccess(res, {
      students,
      count: students.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    sendError(res, `Error fetching student IDs: ${error instanceof Error ? error.message : 'Unknown error'}`, 500);
  }
};