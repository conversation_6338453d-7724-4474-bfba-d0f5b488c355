import express from 'express';
import {
  triggerDailyQuizStreakNotifications,
  getDailyQuizStreakNotificationStatus,
  processStreakNotifications
} from '../controllers/dailyQuizStreakNotificationController';

const router = express.Router();

router.post('/process-streaks', processStreakNotifications);

router.post('/trigger', triggerDailyQuizStreakNotifications);

router.get('/status', getDailyQuizStreakNotificationStatus);



export default router;