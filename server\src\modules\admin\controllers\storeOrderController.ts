import { Request, Response } from 'express';
import { getAllStoreOrders, getStoreOrderById, getStoreOrderStats, updateOrderStatus, getSpinWinners } from '../services/storeOrderService';
import { sendSuccess, sendError } from '@/utils/response';

export const getAllOrders = async (req: Request, res: Response): Promise<void> => {
  try {
    const { status, search, startDate, endDate, modelType, orderType, page, limit } = req.query;

    const orders = await getAllStoreOrders({
      status: status as string,
      search: search as string,
      startDate: startDate as string,
      endDate: endDate as string,
      modelType: modelType as string,
      orderType: orderType as string, 
      page: page ? parseInt(page as string) : 1,
      limit: limit ? parseInt(limit as string) : 10
    });

    sendSuccess(res, orders, 'Store orders retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve store orders', 500);
  }
};

export const getOrderDetails = async (req: Request, res: Response): Promise<void> => {
  try {
    const { orderId } = req.params;
    if (!orderId) {
      sendError(res, 'Order ID is required', 400);
      return;
    }

    const order = await getStoreOrderById(orderId);
    sendSuccess(res, order, 'Order details retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve order details', 500);
  }
};

export const getOrderStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const stats = await getStoreOrderStats();
    sendSuccess(res, stats, 'Order statistics retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve order statistics', 500);
  }
};

export const updateOrder = async (req: Request, res: Response): Promise<void> => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;

    if (!orderId) {
      sendError(res, 'Order ID is required', 400);
      return;
    }

    if (!status) {
      sendError(res, 'Status is required', 400);
      return;
    }

    const order = await updateOrderStatus(orderId, status);
    sendSuccess(res, order, 'Order status updated successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to update order status', 500);
  }
};

export const getSpinWinnersController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { page, limit, search, modelType, rewardName } = req.query;

    const result = await getSpinWinners({
      page: page ? parseInt(page as string) : 1,
      limit: limit ? parseInt(limit as string) : 10,
      search: search as string,
      modelType: modelType as string,
      rewardName: rewardName as string,
    });

    res.status(200).json({
      success: true,
      data: result.winners,
      pagination: {
        currentPage: result.currentPage,
        totalPages: result.totalPages,
        totalCount: result.totalCount,
        limit: result.limit,
      },
    });
  } catch (error: any) {
    console.error("Error fetching spin winners:", error);
    sendError(res, error.message || "Failed to fetch spin winners", 500);
  }
};
