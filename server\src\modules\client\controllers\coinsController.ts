import prisma from "@/config/prismaClient";
import { Request, Response } from "express";
import { createHmac } from "crypto";
import { initRazorpay } from "@/utils/helper";
import { transporter } from "@/utils/email";
import { createCoinPurchaseTemplate, createCoinDeductionTemplate, createCoinReceiptTemplate } from "@/utils/emailTemplates";
import { createNotification } from "@/utils/notifications";
import { UserType, NotificationType } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";
import { addUestCoinTranscation, updateOrCreateUestCoins } from "../services/uestCoinTransactionService";
import { transferCoinsSchema, addCoinsSchema, checkStudentSchema } from "../requests/coinRequest";

export const getCoins = async (req: Request, res: Response): Promise<any> => {
  const classId = req.class?.id;
  const studentId = req.student?.id;
  const modelId = classId || studentId;
  const modelType = classId ? "CLASS" : "STUDENT";

  try {
    const totalCoins = await prisma.uestCoins.aggregate({
      where: { modelId, modelType },
      _sum: { coins: true },
    });

    return res.status(200).json({
      success: true,
      coins: totalCoins._sum.coins || 0,
    });
  } catch (error) {
    console.error("Error fetching coins:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

export const createOrder = async (req: Request, res: Response): Promise<any> => {
  const { amount } = req.body;

  if (!amount || amount > 1000000) {
    return res.status(400).json({ success: false, message: "Invalid amount" });
  }

  try {
    const razorpay = initRazorpay();
    const order = await razorpay.orders.create({
      amount,
      currency: "INR",
      receipt: `receipt_${Date.now()}`,
    });

    return res.status(200).json({ success: true, order });
  } catch (error) {
    console.error("Error creating order:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

export const verify = async (req: Request, res: Response): Promise<any> => {
  const { razorpay_order_id, razorpay_payment_id, razorpay_signature, amount } = req.body;
  const classId = req.class?.id;
  const studentId = req.student?.id;
  const modelId = classId || studentId || "";
  const modelType = classId ? "CLASS" : "STUDENT";

  const generatedSignature = createHmac("sha256", process.env.RAZORPAY_KEY_SECRET!)
    .update(`${razorpay_order_id}|${razorpay_payment_id}`)
    .digest("hex");

  if (generatedSignature !== razorpay_signature) {
    return res.status(400).json({ success: false, message: "Invalid signature" });
  }

  try {
    const updatedCoins = await prisma.uestCoins.upsert({
      where: { modelId_modelType: { modelId, modelType } },
      update: { coins: { increment: amount / 100 } },
      create: { modelId, modelType, coins: amount / 100 },
    });

    await prisma.uestCoinTransaction.create({
      data: {
        modelId,
        modelType,
        type: "CREDIT",
        amount: amount / 100,
        reason: `Coins added via Razorpay ${razorpay_order_id}`,
      },
    });

    let userName = "";
    let userEmail = "";
    if (modelType === "STUDENT") {
      const student = await prisma.student.findUnique({
        where: { id: modelId },
        select: { firstName: true, lastName: true, email: true },
      });
      if (student) {
        userName = `${student.firstName} ${student.lastName}`;
        userEmail = student.email ?? "";
      }
    } else {
      const classDetails = await prisma.classes.findUnique({
        where: { id: modelId },
        select: { firstName: true, lastName: true, email: true, className: true },
      });
      if (classDetails) {
        userName = classDetails.className || `${classDetails.firstName} ${classDetails.lastName}`;
        userEmail = classDetails.email ?? "";
      }
    }

    if (userEmail) {
      try {
        const emailHtml = createCoinPurchaseTemplate({
          name: userName,
          email: userEmail,
          userType: modelType,
          amountPaid: amount / 100,
          coinsAdded: amount / 100,
          totalCoins: updatedCoins.coins,
          paymentId: razorpay_payment_id,
          orderId: razorpay_order_id,
        });

        await transporter.sendMail({
          from: process.env.EMAIL_USER,
          to: userEmail,
          subject: "Uest Coin Purchase Confirmation",
          html: emailHtml,
        });
      } catch (emailError) {
        console.error("Failed to send email:", emailError);
      }
    }

    try {
      const notificationType = modelType === "STUDENT" ? NotificationType.STUDENT_COIN_PURCHASE : NotificationType.CLASS_COIN_PURCHASE;
      await createNotification({
        userId: modelId,
        userType: modelType === "STUDENT" ? UserType.STUDENT : UserType.CLASS,
        type: notificationType,
        title: "Coins Purchased Successfully",
        message: `You have successfully purchased ${amount / 100} coins. Your new balance is ${updatedCoins.coins} coins.`,
        data: {
          amountPaid: amount / 100,
          coinsAdded: amount / 100,
          totalCoins: updatedCoins.coins,
          paymentId: razorpay_payment_id,
          orderId: razorpay_order_id,
        },
      });
    } catch (notificationError) {
      console.error("Failed to create notification:", notificationError);
    }

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error("Error processing coin purchase:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

export const verifyMobile = async (req: Request, res: Response): Promise<any> => {
  const { razorpay_order_id, razorpay_payment_id, amount } = req.body;
  const classId = req.class?.id;
  const studentId = req.student?.id;
  const modelId = classId || studentId || "";
  const modelType = classId ? "CLASS" : "STUDENT";

  try {
    const updatedCoins = await prisma.uestCoins.upsert({
      where: { modelId_modelType: { modelId, modelType } },
      update: { coins: { increment: amount / 100 } },
      create: { modelId, modelType, coins: amount / 100 },
    });

    await prisma.uestCoinTransaction.create({
      data: {
        modelId,
        modelType,
        type: "CREDIT",
        amount: amount / 100,
        reason: `Coins added via Razorpay ${razorpay_order_id}`,
      },
    });

    let userName = "";
    let userEmail = "";
    if (modelType === "STUDENT") {
      const student = await prisma.student.findUnique({
        where: { id: modelId },
        select: { firstName: true, lastName: true, email: true },
      });
      if (student) {
        userName = `${student.firstName} ${student.lastName}`;
        userEmail = student.email ?? "";
      }
    } else {
      const classDetails = await prisma.classes.findUnique({
        where: { id: modelId },
        select: { firstName: true, lastName: true, email: true, className: true },
      });
      if (classDetails) {
        userName = classDetails.className || `${classDetails.firstName} ${classDetails.lastName}`;
        userEmail = classDetails.email ?? "";
      }
    }

    if (userEmail) {
      try {
        const emailHtml = createCoinPurchaseTemplate({
          name: userName,
          email: userEmail,
          userType: modelType,
          amountPaid: amount / 100,
          coinsAdded: amount / 100,
          totalCoins: updatedCoins.coins,
          paymentId: razorpay_payment_id,
          orderId: razorpay_order_id,
        });

        await transporter.sendMail({
          from: process.env.EMAIL_USER,
          to: userEmail,
          subject: "Uest Coin Purchase Confirmation",
          html: emailHtml,
        });
      } catch (emailError) {
        console.error("Failed to send email:", emailError);
      }
    }

    try {
      const notificationType = modelType === "STUDENT" ? NotificationType.STUDENT_COIN_PURCHASE : NotificationType.CLASS_COIN_PURCHASE;
      await createNotification({
        userId: modelId,
        userType: modelType === "STUDENT" ? UserType.STUDENT : UserType.CLASS,
        type: notificationType,
        title: "Coins Purchased Successfully",
        message: `You have successfully purchased ${amount / 100} coins. Your new balance is ${updatedCoins.coins} coins.`,
        data: {
          amountPaid: amount / 100,
          coinsAdded: amount / 100,
          totalCoins: updatedCoins.coins,
          paymentId: razorpay_payment_id,
          orderId: razorpay_order_id,
        },
      });
    } catch (notificationError) {
      console.error("Failed to create notification:", notificationError);
    }

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error("Error processing coin purchase:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

export const addCoins = async (req: Request, res: Response): Promise<any> => {
  let studentId: string;
  let amount: number;
  let reason: string | undefined;

  try {
    const validatedData = addCoinsSchema.parse({
      studentId: req.body.studentId,
      amount: parseFloat(req.body.amount),
      reason: req.body.reason,
    });

    ({ studentId, amount, reason } = validatedData);
  } catch (error: any) {
    return res.status(400).json({
      success: false,
      message: error.errors?.[0]?.message || "Invalid request data",
    });
  }

  try {
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      select: { id: true, firstName: true, lastName: true, email: true },
    });
    if (!student) {
      console.error("Student not found:", studentId);
      return res.status(404).json({
        success: false,
        message: "Student not found.",
      });
    }

    const updatedCoins = await updateOrCreateUestCoins(studentId, "STUDENT", amount);
    const transaction = await addUestCoinTranscation({
      id: uuidv4(),
      modelId: studentId,
      modelType: "STUDENT",
      type: "CREDIT",
      amount: amount,
      reason: reason || "Coins added by admin",
      createdAt: new Date(),
    });

    const userName = `${student.firstName} ${student.lastName}`;
    const userEmail = student.email ?? "";

    if (userEmail) {
      try {
        const emailHtml = createCoinPurchaseTemplate({
          name: userName,
          email: userEmail,
          userType: "STUDENT",
          amountPaid: 0,
          coinsAdded: amount,
          totalCoins: updatedCoins.coins,
          paymentId: "",
          orderId: "",
        });

        await transporter.sendMail({
          from: process.env.EMAIL_USER,
          to: userEmail,
          subject: "Uest Coin Addition Confirmation",
          html: emailHtml,
        });
      } catch (emailError) {
        console.error("Failed to send email:", emailError);
      }
    }

    try {
      await createNotification({
        userId: studentId,
        userType: UserType.STUDENT,
        type: NotificationType.STUDENT_COIN_PURCHASE,
        title: "Coins Added Successfully",
        message: `An admin has added ${amount} coins to your account. Your new balance is ${updatedCoins.coins} coins.`,
        data: {
          amountPaid: 0,
          coinsAdded: amount,
          totalCoins: updatedCoins.coins,
          paymentId: "",
          orderId: "",
        },
      });
    } catch (notificationError) {
      console.error("Failed to create notification:", notificationError);
    }

    return res.status(200).json({
      success: true,
      transaction,
    });
  } catch (error: any) {
    console.error("Error adding coins:", error.message, error.stack);
    return res.status(500).json({
      success: false,
      message: `Internal server error: ${error.message}`,
    });
  }
};

export const transferCoins = async (req: Request, res: Response): Promise<any> => {
  const senderId = req.student?.id;

  if (!senderId) {
    return res.status(401).json({
      success: false,
      message: "Authentication required.",
    });
  }

  let recipientContact: string;
  let amount: number;
  let reason: string | undefined;

  try {
    const validatedData = transferCoinsSchema.parse({
      recipientContact: req.body.recipientContact,
      amount: parseFloat(req.body.amount),
      reason: req.body.reason,
    });

    ({ recipientContact, amount, reason } = validatedData);
  } catch (error: any) {
    return res.status(400).json({
      success: false,
      message: error.errors?.[0]?.message || "Invalid request data",
    });
  }

  try {
    const sender = await prisma.student.findUnique({
      where: { id: senderId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        profile: {
          select: {
            medium: true,
            classroom: true,
            birthday: true,
            school: true,
            address: true,
          }
        }
      },
    });

    if (!sender) {
      return res.status(404).json({
        success: false,
        message: "Sender not found.",
      });
    }

    const profile = sender.profile;
    if (!profile || !profile.medium || !profile.classroom || !profile.birthday || !profile.school || !profile.address) {
      return res.status(400).json({
        success: false,
        message: "Please complete your profile before transferring coins. Required fields: medium, classroom, birthday, school, and address.",
      });
    }

    const senderCoins = await prisma.uestCoins.findUnique({
      where: { modelId_modelType: { modelId: senderId, modelType: "STUDENT" } },
    });
    if (!senderCoins || senderCoins.coins < amount) {
      return res.status(400).json({
        success: false,
        message: "Insufficient coins.",
      });
    }

    const recipient = await prisma.student.findFirst({
      where: { contact: recipientContact },
      select: { id: true, firstName: true, lastName: true, email: true },
    });
    if (!recipient) {
      return res.status(404).json({
        success: false,
        message: "Recipient not found.",
      });
    }

    const result = await prisma.$transaction(async (tx) => {
      const senderUpdated = await tx.uestCoins.update({
        where: { modelId_modelType: { modelId: senderId, modelType: "STUDENT" } },
        data: { coins: { decrement: amount } },
      });

      const recipientUpdated = await updateOrCreateUestCoins(recipient.id, "STUDENT", amount);

      await addUestCoinTranscation({
        id: uuidv4(),
        modelId: senderId,
        modelType: "STUDENT",
        type: "DEBIT",
        amount,
        reason: reason || `Coins transferred to ${recipient.firstName} ${recipient.lastName}`,
        createdAt: new Date(),
      });

      await addUestCoinTranscation({
        id: uuidv4(),
        modelId: recipient.id,
        modelType: "STUDENT",
        type: "CREDIT",
        amount,
        reason: reason || `Coins received from ${sender.firstName} ${sender.lastName}`,
        createdAt: new Date(),
      });

      return { senderUpdated, recipientUpdated };
    });

    if (sender.email) {
      try {
        const emailHtml = createCoinDeductionTemplate({
          senderName: `${sender.firstName} ${sender.lastName}`,
          recipientName: `${recipient.firstName} ${recipient.lastName}`,
          amount,
          totalCoins: result.senderUpdated.coins,
          reason,
        });

        await transporter.sendMail({
          from: process.env.EMAIL_USER,
          to: sender.email,
          subject: "Coin Transfer Confirmation",
          html: emailHtml,
        });
      } catch (emailError) {
        console.error("Failed to send email to sender:", emailError);
      }
    }

    if (recipient.email) {
      try {
        const emailHtml = createCoinReceiptTemplate({
          senderName: `${sender.firstName} ${sender.lastName}`,
          recipientName: `${recipient.firstName} ${recipient.lastName}`,
          amount,
          totalCoins: result.recipientUpdated.coins,
          reason,
        });

        await transporter.sendMail({
          from: process.env.EMAIL_USER,
          to: recipient.email,
          subject: "Coins Received",
          html: emailHtml,
        });
      } catch (emailError) {
        console.error("Failed to send email to recipient:", emailError);
      }
    }

    try {
      await createNotification({
        userId: senderId,
        userType: UserType.STUDENT,
        type: NotificationType.STUDENT_COIN_PURCHASE,
        title: "Coins Transferred Successfully",
        message: `You have transferred ${amount} coins to ${recipient.firstName} ${recipient.lastName}. Your new balance is ${result.senderUpdated.coins} coins.`,
        data: { amount, totalCoins: result.senderUpdated.coins },
      });

      await createNotification({
        userId: recipient.id,
        userType: UserType.STUDENT,
        type: NotificationType.STUDENT_COIN_PURCHASE,
        title: "Coins Received Successfully",
        message: `You have received ${amount} coins from ${sender.firstName} ${sender.lastName}. Your new balance is ${result.recipientUpdated.coins} coins.`,
        data: { amount, totalCoins: result.recipientUpdated.coins },
      });
    } catch (notificationError) {
      console.error("Failed to create notification:", notificationError);
    }

    return res.status(200).json({
      success: true,
      message: `Coins transferred successfully to ${recipient.firstName} ${recipient.lastName}.`,
    });
  } catch (error: any) {
    console.error("Error transferring coins:", error.message, error.stack);
    return res.status(500).json({
      success: false,
      message: `Internal server error: ${error.message}`,
    });
  }
};

export const checkStudent = async (req: Request, res: Response): Promise<any> => {
  try {
    const validatedData = checkStudentSchema.parse({
      contact: req.body.contact,
    });

    const { contact } = validatedData;

    const student = await prisma.student.findFirst({
      where: { contact },
      select: { id: true, firstName: true, lastName: true },
    });

    return res.status(200).json({
      success: true,
      exists: !!student,
      message: student ? "Student found" : "Student not found",
      studentName: student ? `${student.firstName} ${student.lastName}` : null,
    });
  } catch (error: any) {
    if (error.errors) {
      return res.status(400).json({
        success: false,
        message: error.errors[0]?.message || "Invalid request data",
      });
    }

    console.error("Error checking student:", error.message, error.stack);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};