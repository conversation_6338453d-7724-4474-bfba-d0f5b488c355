'use client';
import React, { useEffect, useState } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardTitle } from '@/components/ui/card';
import { DataTable } from '@/app-components/dataTable';
import Pagination from '@/app-components/pagination';
import { format } from 'date-fns';
import BadgeDisplay from '@/components/ui/badgedisplay';
import Image from 'next/image';
import { getMockExamResults } from '@/services/dailyQuizResult';
import { Button } from '@/components/ui/button';

interface MockExamResult {
  id: string;
  studentId: string;
  score: number;
  coinEarnings: number;
  streakId: string | null;
  streakCount?: number;
  createdAt: string;
  updatedAt: string;
}

const MockExamResults: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const studentId = params.id as string;
  const firstName = searchParams.get('firstName') || '';
  const lastName = searchParams.get('lastName') || '';
  const [results, setResults] = useState<MockExamResult[]>([]);
  const [streakCount, setStreakCount] = useState(0);
  const [totalCoinsEarned, setTotalCoinsEarned] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [statsLoading, setStatsLoading] = useState(true);
  const [, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [coinBadgeSrc, setCoinBadgeSrc] = useState<string | null>(null);
  const [coinBadgeAlt, setCoinBadgeAlt] = useState<string | null>(null);
  const [streakBadge, setStreakBadge] = useState<any>(null);
  const PAGE_SIZE = 10;

  const columns: ColumnDef<MockExamResult>[] = [
    {
      accessorKey: "createdAt",
      header: "Date",
      cell: ({ row }) => {
        const date = new Date(row.original.createdAt);
        return (
          <div className="text-sm font-medium text-gray-900">
            {format(date, 'MMM dd, yyyy')}
          </div>
        );
      }
    },
    {
      accessorKey: "score",
      header: "Score",
    },
    {
      accessorKey: "coinEarnings",
      header: "Coin Earnings",
    },
  ];

  const fetchAllResultsForStats = async () => {
    try {
      let allResults: MockExamResult[] = [];
      let currentPageForStats = 1;
      let totalPagesForStats = 1;
      let streakCountFromAPI = 0;
      let streakBadgeFromAPI = null;
      do {
        const response = await getMockExamResults(studentId, currentPageForStats, PAGE_SIZE);
        if (response.success && response.data?.data) {
          const { data, pagination } = response.data;
          allResults = [...allResults, ...(data.mockExamResults || [])];
          totalPagesForStats = pagination.totalPages || 1;

          if (currentPageForStats === 1) {
            streakCountFromAPI = data.badge?.streakCount ||
              data.streakCount ||
              (data.mockExamResults?.[0]?.streakCount) || 0;

            if (data.badge) {
              streakBadgeFromAPI = data.badge;
            } else if (data.streakBadge) {
              streakBadgeFromAPI = {
                streakCount: streakCountFromAPI,
                badges: [data.streakBadge],
                badgeType: data.streakBadge.badgeType || null,
                badgeSrc: data.streakBadge.badgeSrc || null,
                badgeAlt: data.streakBadge.badgeAlt || null
              };
            }
          }
          currentPageForStats++;
        } else {
          break;
        }
      } while (currentPageForStats <= totalPagesForStats);

      const totalCoins = allResults.reduce(
        (sum: number, result: MockExamResult) => sum + (result.coinEarnings || 0),
        0
      );
      setStreakCount(streakCountFromAPI);
      setTotalCoinsEarned(totalCoins);
      setStreakBadge(streakBadgeFromAPI);

      let badgeSrc = null;
      let badgeAlt = null;
      if (totalCoins >= 100 && totalCoins <= 499) {
        badgeSrc = "/scholer.svg";
        badgeAlt = "Scholar Badge";
      } else if (totalCoins >= 500 && totalCoins <= 999) {
        badgeSrc = "/Mastermind.svg";
        badgeAlt = "Mastermind Badge";
      } else if (totalCoins >= 1000) {
        badgeSrc = "/Achiever.svg";
        badgeAlt = "Achiever Badge";
      }
      setCoinBadgeSrc(badgeSrc);
      setCoinBadgeAlt(badgeAlt);

    } catch (err: any) {
      console.error('Error fetching all results for stats:', err);
    }
  };

  const fetchMockExamResults = async (page: number = 1) => {
    try {
      setIsLoading(true);
      const response = await getMockExamResults(studentId, page, PAGE_SIZE);

      if (response.success && response.data?.data) {
        const { data, pagination } = response.data;
        setResults(data.mockExamResults || []);
        setTotalPages(pagination.totalPages || 1);
        setCurrentPage(pagination.currentPage || page);

        if (page === 1 || totalCoinsEarned === 0) {
          await fetchAllResultsForStats();
        }
      } else {
        setError(response.error || 'Failed to fetch results');
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred');
      console.error('Error fetching mock exam results:', err);
    } finally {
      setIsLoading(false);
      setStatsLoading(false);
    }
  };

  useEffect(() => {
    if (studentId) {
      fetchMockExamResults(currentPage);
    }
  }, [studentId, currentPage]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  if (!studentId) {
    return <div className="p-4">No student provided</div>;
  }

  return (
    <div className="p-4">
      <Button
        variant="ghost"
        size="icon"
        onClick={() => router.back()}
        className="hover:bg-gray-100"
      >
        <ArrowLeft className="h-4 w-4" />
      </Button>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Daily Quiz Details - {firstName} {lastName}</h1>
        </div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-6">
        <Card className="bg-white rounded-xl shadow-md">
          <CardContent className="flex flex-col justify-center h-24 px-5">
            <div className="flex items-center justify-center">
              <div className="flex items-center space-x-2">
                <CardTitle className="text-center font-medium text-gray-700 tracking-wide">
                  Streak Count
                </CardTitle>
              </div>
            </div>
            <CardDescription className="text-xl font-semibold text-black text-center">
              {statsLoading ? (
                <Loader2 className="h-6 w-6 animate-spin mx-auto" />
              ) : (
                streakCount
              )}
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="bg-white rounded-xl shadow-md">
          <CardContent className="flex flex-col justify-center h-24 px-5">
            <div className="flex items-center justify-center">
              <div className="flex items-center space-x-2">
                <CardTitle className="text-center font-medium text-gray-700 tracking-wide">
                  Total Coins Earned
                </CardTitle>
              </div>
            </div>
            <CardDescription className="flex items-center justify-center gap-2 mt-2">
              {statsLoading ? (
                <Loader2 className="h-6 w-6 animate-spin mx-auto" />
              ) : (
                <span className="text-xl font-semibold text-black">
                  {totalCoinsEarned.toLocaleString()}
                </span>
              )}
            </CardDescription>
          </CardContent>
        </Card>
        <Card className="bg-white rounded-xl shadow-md">
          <CardContent className="flex flex-col justify-center h-24 px-5">
            <div className="flex items-center justify-center">
              <div className="flex items-center space-x-2">
                <CardTitle className="text-center font-medium text-gray-700 tracking-wide">
                  Badges Earned
                </CardTitle>
              </div>
            </div>
            <CardDescription className="flex items-center justify-center gap-3 mt-2">
              {statsLoading ? (
                <Loader2 className="h-6 w-6 animate-spin mx-auto" />
              ) : (
                <>
                  {coinBadgeSrc ? (
                    <div className="flex items-center gap-1">
                      <Image
                        src={coinBadgeSrc}
                        alt={coinBadgeAlt || 'Badge'}
                        width={38}
                        height={38}
                        className="object-contain"
                      />
                    </div>
                  ) : null}
                  {streakBadge && <BadgeDisplay badge={streakBadge} />}
                  {!coinBadgeSrc && !streakBadge && (
                    <span className="text-sm text-gray-500">No badges earned yet</span>
                  )}
                </>
              )}
            </CardDescription>
          </CardContent>
        </Card>
      </div>
      <hr />
      <div className="mt-6">
        {isLoading ? (
          <div className="flex justify-center items-center p-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <DataTable
            columns={columns}
            data={results}
            isLoading={isLoading}
          />
        )}
      </div>
      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={handlePageChange}
        entriesText={`${results.length} entries`}
      />
    </div>
  );
};

export default MockExamResults;