"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ShoppingBag, Package, Clock, CheckCircle, XCircle, Coins, Calendar } from "lucide-react";
import { toast } from "sonner";
import { isAuthenticated } from "@/lib/utils";
import * as storePurchaseApi from "@/services/storePurchaseApi";
import Image from "next/image";

interface MyOrdersProps {
  userType: 'STUDENT' | 'CLASS';
  loginPath: string;
}

export default function MyOrders({ userType, loginPath }: MyOrdersProps) {
  const [orders, setOrders] = useState<storePurchaseApi.StoreOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [isUserLoggedIn, setIsUserLoggedIn] = useState(false);

  useEffect(() => {
    const authStatus = isAuthenticated();
    setIsUserLoggedIn(authStatus.isAuth);
    
    if (authStatus.isAuth && authStatus.userType === userType) {
      loadOrders();
    } else {
      setLoading(false);
      if (!authStatus.isAuth) {
        toast.error("Please login to view your orders");
      } else {
        toast.error(`Access denied. This page is for ${userType.toLowerCase()}s only.`);
      }
    }
  }, [userType]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      const result = await storePurchaseApi.getMyOrders();
      
      if (result.success && result.data) {
        setOrders(result.data);
      } else {
        toast.error(result.error || "Failed to load orders");
      }
    } catch (error) {
      console.error('Error loading orders:', error);
      toast.error("Failed to load orders");
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Clock className="w-4 h-4" />;
      case 'COMPLETED':
        return <CheckCircle className="w-4 h-4" />;
      case 'CANCELLED':
        return <XCircle className="w-4 h-4" />;
      default:
        return <Package className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isUserLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <ShoppingBag className="w-16 h-16 mx-auto text-gray-400 mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Login Required</h2>
          <p className="text-gray-600 mb-4">Please login to view your orders</p>
          <Button onClick={() => window.location.href = loginPath}>
            Login
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <div className="bg-background dark:bg-gray-900 py-12 border-b">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-3 bg-customOrange/10 rounded-xl">
              <ShoppingBag className="w-8 h-8 text-customOrange" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-foreground">My Orders</h1>
              <p className="text-muted-foreground">Track and manage your store orders</p>
            </div>
          </div>
        </div>
      </div>
    
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <Skeleton className="w-16 h-16 rounded" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-1/3" />
                      <Skeleton className="h-4 w-1/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                    <Skeleton className="h-8 w-20" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : orders.length === 0 ? (
          <div className="text-center py-12">
            <ShoppingBag className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No Orders Yet</h3>
            <p className="text-gray-600 mb-6">You haven&apos;t placed any orders yet. Start shopping to see your orders here!</p>
            <Button onClick={() => window.location.href = '/store'} className="bg-customOrange hover:bg-orange-600">
              <ShoppingBag className="w-4 h-4 mr-2" />
              Browse Store
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {orders.map((order) => (
              <Card key={order.id} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-customOrange/10 rounded-lg">
                        <Package className="w-5 h-5 text-customOrange" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">Order #{order.id.slice(-8)}</CardTitle>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Calendar className="w-4 h-4" />
                          {formatDate(order.createdAt)}
                        </div>
                      </div>
                    </div>
                    <Badge className={`${getStatusColor(order.status)} flex items-center gap-1`}>
                      {getStatusIcon(order.status)}
                      {order.status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4">
                    {(order.itemName === "Keychain" || order.itemName === "1 notebook") ? (
                      <div className="w-16 h-16 rounded-lg bg-gray-100 flex items-center justify-center">
                        <Package className="w-8 h-8 text-gray-400" />
                      </div>
                    ) : (
                      <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-gray-100">
                        <Image
                          src={
                            order.item?.image?.startsWith('http')
                              ? order.item.image
                              : order.item?.image
                              ? `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${order.item.image?.startsWith('/') ? order.item.image.substring(1) : order.item.image}`
                              : "/logo.png"
                          }
                          alt={order.itemName}
                          fill
                          className="object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = "/logo.png";
                          }}
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <h4 className="font-medium text-foreground">{order.itemName}</h4>
                      <p className="text-sm text-muted-foreground">
                        Quantity: {order.quantity} × {order.itemPrice} coins
                      </p>
                      {order.item?.category && (
                        <Badge variant="outline" className="mt-1 text-xs">
                          {order.item.category}
                        </Badge>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-1 text-lg font-semibold text-customOrange">
                        <Coins className="w-5 h-5" />
                        {order.totalCoins}
                      </div>
                      <p className="text-sm text-muted-foreground">Total</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
